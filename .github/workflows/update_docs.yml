---
name: Update docs

on: # yamllint disable-line rule:truthy
  push:
    branches:
      - release-please--branches--main

permissions: read-all

jobs:
  docs:
    # update docs after merge back to develop
    name: Auto update terraform docs
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout branch
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4 # ratchet:actions/checkout@v3
      - name: Generate TF docs
        uses: terraform-docs/gh-actions@aeae0038ed47a547e0c0fca5c059d3335f48fb25 # v1.3.0
        with:
          find-dir: .
          git-commit-message: "docs: auto update terraform docs"
          git-push: true
