---
name: Approve all Renovate PRs automatically

# yamllint disable-line rule:truthy
on: pull_request_target

permissions:
  contents: read
  
jobs:
  auto-approve:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    if: github.actor == 'renovate[bot]'
    steps:
      - uses: hmarr/auto-approve-action@f0939ea97e9205ef24d872e76833fa908a770363 # v4.0.0
        with:
          review-message: "Auto approved Renovate PR by organization"
          github-token: ${{ secrets.AUTO_APPROVE_TOKEN }}
