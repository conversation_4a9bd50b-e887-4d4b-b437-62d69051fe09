---
name: Execute ChatOps command

on: # yamllint disable-line rule:truthy
  repository_dispatch:
    types:
      - help-command

permissions: read-all

jobs:
  help-command:
    name: "ChatOps: /help"
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Choose maintainer
        id: vars
        run: |
          maintainer=$(grep -oE "@[a-zA-Z0-9_-]+" CODEOWNERS | shuf -n 1)
          echo "maintainer=$maintainer" >> "$GITHUB_OUTPUT"
      - name: Create comment
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7
        with:
          script: |
            // adds a comment to the PR (there is the issue API, which works work PRs too)
            github.rest.issues.createComment({
              issue_number: context.payload.client_payload.github.payload.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'Hey there ${{ steps.vars.outputs.maintainer }}, could you please help @${{ github.event.client_payload.github.payload.comment.user.login }} out?'
            })
