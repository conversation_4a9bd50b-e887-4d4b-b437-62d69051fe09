---
name: 'Close stale issues and PRs'

on: # yamllint disable-line rule:truthy
  schedule:
    - cron: '25 2 * * *'

permissions: read-all

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639 # v9
        with:
          stale-issue-message: 'This issue is stale because it has been open 60 days with no activity. Remove stale label or comment or this will be closed in 15 days.'
          stale-pr-message: 'This PR is stale because it has been open 60 days with no activity. Remove stale label or comment or this will be closed in 15 days.'
          close-issue-message: 'This issue was closed because it has been stalled for 15 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for 15 days with no activity.'
          days-before-stale: 60
          days-before-close: 15
          exempt-issue-labels: 'work-in-progress'
          exempt-pr-labels: 'work-in-progress'
          stale-issue-label: 'stale'
          stale-pr-label: 'stale'
