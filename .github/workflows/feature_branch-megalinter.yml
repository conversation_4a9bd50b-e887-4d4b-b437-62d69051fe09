---
name: Feature branch MegaLinter

on: # yamllint disable-line rule:truthy
  pull_request:

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  linter:
    name: <PERSON><PERSON>inter
    runs-on: ubuntu-latest
    steps:
      # Git Checkout
      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0 # If you use VALIDATE_ALL_CODEBASE = true, you can remove this line to improve performances

      # MegaLinter
      - name: MegaLinter
        id: ml
        # You can override MegaLinter flavor used to have faster performances
        # More info at https://megalinter.io/flavors/
        uses: oxsecurity/megalinter@ec124f7998718d79379a3c5b39f5359952baf21d # v8.4.2
        env:
          # All available variables are described in documentation
          # https://megalinter.io/configuration/
          VALIDATE_ALL_CODEBASE: false
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # ADD YOUR CUSTOM ENV VARIABLES HERE OR DEFINE THEM IN A FILE .mega-linter.yml AT THE ROOT OF YOUR REPOSITORY
          SPELL_CSPELL_FILTER_REGEX_EXCLUDE: (\.gitignore|.tflint.hcl|CHANGELOG.md)
          # needed to avoid multiple error messages
          TERRAFORM_TERRASCAN_ARGUMENTS: "--non-recursive"
          # format issues fail the build
          TERRAFORM_TERRAFORM_FMT_DISABLE_ERRORS: false
          # it's an auto-generated file
          MARKDOWN_MARKDOWNLINT_FILTER_REGEX_EXCLUDE: (CHANGELOG.md)
          # it's an auto-generated file
          MARKDOWN_MARKDOWN_LINK_CHECK_FILTER_REGEX_EXCLUDE: (CHANGELOG.md)
          # automatically commit fixes to the feature branch
          APPLY_FIXES: all
          APPLY_FIXES_EVENT: pull_request
          APPLY_FIXES_MODE: commit
      # Upload MegaLinter artifacts
      - name: Archive production artifacts
        if: ${{ success() || failure() }}
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        with:
          name: MegaLinter reports
          path: |
            megalinter-reports
            mega-linter.log

