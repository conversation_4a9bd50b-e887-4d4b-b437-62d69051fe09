---
name: CI

on: # yamllint disable-line rule:truthy
  pull_request:

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

permissions:
  contents: read
  pull-requests: write

env:
  TF_PLUGIN_CACHE_DIR: ${{ github.workspace }}/.terraform.d/plugin-cache

jobs:
  verify_module:
    name: Verify module
    strategy:
      matrix:
        terraform: [1.3.9]
    runs-on: ubuntu-latest
    container:
      image: hashicorp/terraform:${{ matrix.terraform }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - run: terraform init -get -backend=false -input=false
      - run: terraform fmt -recursive -check=true -write=false

  verify_examples:
    name: Verify examples
    strategy:
      fail-fast: false
      matrix:
        terraform: [1.3.9, latest]
        example:
          [
            "runner-default",
            "runner-docker",
            "runner-fleeting-plugin",
            "runner-public",
            "runner-certificates",
          ]
    defaults:
      run:
        working-directory: examples/${{ matrix.example }}
    runs-on: ubuntu-latest
    container:
      image: hashicorp/terraform:${{ matrix.terraform }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - run: terraform init -get -backend=false -input=false
      - if: contains(matrix.terraform, '1.3.')
        run: terraform fmt -recursive -check=true -write=false
      - run: terraform validate

  kics:
    runs-on: ubuntu-latest
    container:
      image: checkmarx/kics:v2.1.6-debian@sha256:042f1f1aab3b9b7399e28f96160b9485c4a32b98cf2eec3b68b08cb5bb606b1e
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      # ignore: "tags not used", "access analyzer not used", "shield advanced not used"
      - run: kics scan -p . -o . --config .kics.yml --exclude-queries e38a8e0a-b88b-4902-b3fe-b0fcb17d5c10,e592a0c5-5bdb-414c-9066-5dba7cdea370,084c6686-2a70-4710-91b1-000393e54c12

  tflint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        name: Checkout source code

      - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4
        name: Cache plugin dir
        with:
          path: ~/.tflint.d/plugins
          key: tflint-${{ hashFiles('.tflint.hcl') }}

      - uses: terraform-linters/setup-tflint@90f302c255ef959cbfb4bd10581afecdb7ece3e6 # v4
        name: Setup TFLint
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tflint_version: latest

      - name: Show version
        run: tflint --version

      - name: Init TFLint
        run: tflint --init

      - name: Run TFLint
        # assign necessary variables to avoid errors
        run: 'tflint --var ''enable_managed_kms_key=true'' --var=''runner_instance={"name_prefix": "a", "name": "b"}'''

  tfsec:
    name: tfsec PR commenter
    runs-on: ubuntu-latest

    steps:
      - name: Clone repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: tfsec
        uses: aquasecurity/tfsec-pr-commenter-action@7a44c5dcde5dfab737363e391800629e27b6376b # v1.3.1
        with:
          github_token: ${{ github.token }}
