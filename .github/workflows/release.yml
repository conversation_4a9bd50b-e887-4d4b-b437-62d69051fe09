---
name: Release

on: # yamllint disable-line rule:truthy
  push:
    branches:
      - main

permissions: read-all

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Get app installation token
        uses: npalm/action-app-token@dd4bb16d91ced5659bc618705c96b822c5a42136 # ratchet:npalm/action-app-token@v1.1.0
        id: token
        with:
          appId: ${{ secrets.RELEASER_APP_ID }}
          appPrivateKeyBase64: ${{ secrets.RELEASER_APP_PRIVATE_KEY_BASE64 }}
          appInstallationType: repo
          appInstallationValue: ${{ github.repository }}
      # bootstrap-sha and release-as needs to be removed after first release
      - name: Release
        uses: google-github-actions/release-please-action@e4dc86ba9405554aeba3c6bb2d169500e7d3b4ee # v4
        with:
          token: ${{ steps.token.outputs.token }}
          config-file: ".github/.release-please-config.json"
          manifest-file: ".github/.release-please-manifest.json"
