name: Release

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - master
    paths:
      - '**/*.tpl'
      - '**/*.py'
      - '**/*.tf'
      - '.github/workflows/release.yml'

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    # Skip running release workflow on forks
    if: github.repository_owner == 'terraform-aws-modules'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false
          fetch-depth: 0

      - name: Release
        uses: cycjimmy/semantic-release-action@v4
        with:
          semantic_version: 23.0.2
          extra_plugins: |
            @semantic-release/changelog@6.0.3
            @semantic-release/git@10.0.1
            conventional-changelog-conventionalcommits@7.0.2
        env:
          GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN }}
