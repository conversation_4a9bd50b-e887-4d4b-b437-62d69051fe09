{"version": "0.2", "language": "en", "ignoreWords": ["alltrue", "amazonec", "anytrue", "amannn", "autonumber", "awscli", "boto", "botocore", "buildx", "certdir", "checkmarx", "concat", "containerd", "devskim", "dind", "dpkg", "endfor", "filesha", "formatlist", "glrt", "glrunners", "hmarr", "icmpv6", "instancelifecycle", "keyrings", "kics", "joho", "jsonencode", "markdownlint", "matchDatasources", "mypy", "noexec", "nolint", "npalm", "platformAutomerge", "pylint", "pylintrc", "pyright", "setsubtract", "shuf", "signum", "stretchr", "subkey", "substr", "sysctl", "sysctls", "templatefile", "terrascan", "terratest", "tfenv", "tflint", "tftpl", "tfsec", "tftest", "tftpl", "tfvars", "tmpfs", "tonumber", "trimprefix", "trivy", "usermod", "userns", "vcpu", "xanzy", "xvda"], "words": ["aquasecurity", "automerge", "autoscaler", "backports", "blockquotes", "bluegreen", "burstable", "codeowners", "cpu", "cpus", "cpuset", "gitter", "<PERSON><PERSON>", "oxsecurity", "rebalance", "signoff", "typecheck", "userdata"], "flagWords": []}