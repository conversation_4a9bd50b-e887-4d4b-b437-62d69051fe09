[SERVICE]
    Parsers_File /parsers.conf
    Flush 1
    Grace 30

[FILTER]
    Name parser
    Match BillPaymentAPI*
    Key_Name log
    Parser json
    Reserve_Data True

[FILTER]
    name                  multiline
    match                 BillPaymentAPI*
    multiline.key_content log
    multiline.parser      java

[OUTPUT]
    Name                cloudwatch
    Match               BillPaymentAPI*
    region              us-east-1
    log_group_name      /logs/bill-payment-task
    log_stream_prefix   ecs/

[OUTPUT]
    Name              datadog
    Match             *
    Host              http-intake.logs.datadoghq.com
    TLS               on
    compress          gzip
    apikey            2edbdaf52ee863abdb422f7cdffb8131
    dd_service        bill-payment-service
    dd_source         logback
    dd_message_key    log
    dd_tags           env:production
    provider          ecs