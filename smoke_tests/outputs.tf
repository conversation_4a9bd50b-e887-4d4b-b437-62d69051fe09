output "python_3_13_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-python-3-13.arn
}

output "python_3_13_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-python-3-13.invoke_arn
}

output "python_3_13_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-python-3-13.function_name
}


output "python_3_12_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-python-3-12.arn
}

output "python_3_12_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-python-3-12.invoke_arn
}

output "python_3_12_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-python-3-12.function_name
}


output "python_3_11_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-python-3-11.arn
}

output "python_3_11_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-python-3-11.invoke_arn
}

output "python_3_11_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-python-3-11.function_name
}


output "python_3_10_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-python-3-10.arn
}

output "python_3_10_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-python-3-10.invoke_arn
}

output "python_3_10_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-python-3-10.function_name
}


output "python_3_9_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-python-3-9.arn
}

output "python_3_9_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-python-3-9.invoke_arn
}

output "python_3_9_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-python-3-9.function_name
}


output "python_3_8_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-python-3-8.arn
}

output "python_3_8_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-python-3-8.invoke_arn
}

output "python_3_8_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-python-3-8.function_name
}


output "node_22_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-node-22.arn
}

output "node_22_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-node-22.invoke_arn
}

output "node_22_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-node-22.function_name
}


output "node_20_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-node-20.arn
}

output "node_20_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-node-20.invoke_arn
}

output "node_20_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-node-20.function_name
}


output "node_18_arn" {
  description = "Amazon Resource Name (ARN) identifying your Lambda Function."
  value       = module.lambda-node-18.arn
}

output "node_18_invoke_arn" {
  description = "ARN to be used for invoking Lambda Function from API Gateway."
  value       = module.lambda-node-18.invoke_arn
}

output "node_18_function_name" {
  description = "Unique name for your Lambda Function"
  value       = module.lambda-node-18.function_name
}
