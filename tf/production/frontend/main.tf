locals {
  account_id                    = "************"
  production_availability_zones = ["us-east-1a", "us-east-1b"]
}

provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [local.account_id]
}

module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "via1-production-infrastructure"
  dynamodb_table_name = "frontend-infrastructure-lock-table"
}

terraform {
  backend "s3" {
    bucket         = "via1-production-infrastructure"
    key            = "frontend/terraform.tfstate"
    dynamodb_table = "frontend-infrastructure-lock-table"
    encrypt        = true
    region         = "us-east-1"
  }
}

resource "aws_cloudfront_origin_access_identity" "webapp_origin_access_identity" {
  comment = "Webapp Origin Access Identity"
}

resource "aws_s3_bucket" "webapp_production" {
  bucket = "via1-webapp-production"
  acl    = "private"
  policy = templatefile("bucket_policy.json", {
    user    = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.iam_arn
    bucket  = "via1-webapp-production"
    pattern = "current/*"
  })
  lifecycle {
    prevent_destroy = true
  }
  lifecycle_rule {
    abort_incomplete_multipart_upload_days = 0
    enabled                                = true
    id                                     = "ExpireOldWebappVersions"
    tags                                   = {}
    expiration {
      days                         = 180
      expired_object_delete_marker = false
    }
    noncurrent_version_expiration {
      days = 1
    }
  }
  lifecycle_rule {
    abort_incomplete_multipart_upload_days = 0
    enabled                                = true
    id                                     = "DeleteExpiredDeleteMarkers"
    tags                                   = {}

    expiration {
      days                         = 0
      expired_object_delete_marker = true
    }
  }

versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "webapp_production" {
  bucket = aws_s3_bucket.webapp_production.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "webapp_production" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.webapp_production.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.webapp_production.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.webapp_production.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:8"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:8"
    }
  }
  aliases = ["use.via1.app"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  custom_error_response {
    error_code            = 403
    error_caching_min_ttl = 300
    response_code         = 200
    response_page_path    = "/index.html"
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  // FIXME quando tiver o recurso usar ele aqui
  web_acl_id              = "arn:aws:wafv2:us-east-1:************:global/webacl/WAF_Default_ACL/02b7ce70-1769-4b9c-b1f2-66f37a68ee9f"
  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudfront_distribution" "friday_webapp_production" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.webapp_production.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.webapp_production.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.webapp_production.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:8"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:8"
    }
  }
  aliases = ["use.friday.ai"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  custom_error_response {
    error_code            = 403
    error_caching_min_ttl = 300
    response_code         = 200
    response_page_path    = "/index.html"
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = "arn:aws:acm:us-east-1:************:certificate/9e4015a8-1571-46d8-b034-57db1a9b6c4d" # friday certificate
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  // FIXME quando tiver o recurso usar ele aqui
  web_acl_id              = "arn:aws:wafv2:us-east-1:************:global/webacl/WAF_Default_ACL/02b7ce70-1769-4b9c-b1f2-66f37a68ee9f"
  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudfront_origin_access_identity" "site_origin_access_identity" {
  comment = "Site Origin Access Identity"
}

resource "aws_s3_bucket" "site_production" {
  bucket = "via1-website-production"
  acl    = "private"
  policy = templatefile("bucket_policy.json", {
    user    = aws_cloudfront_origin_access_identity.site_origin_access_identity.iam_arn
    bucket  = "via1-website-production"
    pattern = "current/*"
  })
  lifecycle {
    prevent_destroy = false
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "site_production" {
  bucket = aws_s3_bucket.site_production.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "site_production" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.site_production.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.site_production.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.site_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.site_production.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:8"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:8"
    }
  }
  aliases = ["via1.app", "www.via1.app"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }
  // FIXME quando tiver o recurso usar ele aqui
  web_acl_id              = "arn:aws:wafv2:us-east-1:************:global/webacl/WAF_Default_ACL/02b7ce70-1769-4b9c-b1f2-66f37a68ee9f"

  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudfront_origin_access_identity" "notification_templates_origin_access_identity" {
  comment = "Notification Templates Identity"
}

resource "aws_s3_bucket" "notification_templates_production" {
  bucket = "via1-notification-templates-production"
  acl    = "private"
  policy = templatefile("bucket_policy.json", {
    user    = aws_cloudfront_origin_access_identity.notification_templates_origin_access_identity.iam_arn
    bucket  = "via1-notification-templates-production"
    pattern = "current/static/*"
  })
  lifecycle {
    prevent_destroy = false
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "notification_templates_production" {
  bucket = aws_s3_bucket.notification_templates_production.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "notification_templates_production" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.notification_templates_production.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.notification_templates_production.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.notification_templates_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.notification_templates_production.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:8"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:8"
    }
  }
  aliases = ["notification-templates-cdn.via1.app"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  tags = {
    Environment = var.environment
  }
}

/*
 * Lambda@Edge Static Proxies - Bucket
 */

resource "aws_s3_bucket" "static_lambda_proxy_production" {
  bucket = "via1-static-lambda-proxy-production"
  acl    = "private"
  lifecycle {
    prevent_destroy = false
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "static_lambda_proxy_production" {
  bucket = aws_s3_bucket.static_lambda_proxy_production.id

  block_public_acls   = true
  block_public_policy = true
}

/*
 * Lambda@Edge Static Proxies - Origin Request
 */

data "aws_iam_policy_document" "static_lambda_execution_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com", "edgelambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "static_lambda_proxy_origin_request" {
  name               = "static_lambda_proxy_origin_request"
  assume_role_policy = data.aws_iam_policy_document.static_lambda_execution_role.json
}

resource "aws_cloudwatch_log_group" "static_lambda_proxy_origin_request" {
  name              = "/aws/lambda/static-lambda-proxy-origin-request"
  retention_in_days = 14
}

resource "aws_iam_policy" "static_lambda_proxy_origin_request_logging" {
  name        = "static-lambda-proxy-origin-request-logging"
  path        = "/"
  description = "IAM policy for logging from a static lambda proxy for origin request"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${var.aws_region}:*:log-group:/aws/lambda/static-lambda-proxy-origin-request:*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "static_lambda_proxy_origin_request_logging" {
  role       = aws_iam_role.static_lambda_proxy_origin_request.name
  policy_arn = aws_iam_policy.static_lambda_proxy_origin_request_logging.arn
}

// TODO: there is no assurance lambda is on correct region
resource "aws_lambda_function" "static_lambda_proxy_origin_request" {
  function_name = "static-lambda-proxy-origin-request"
  role          = aws_iam_role.static_lambda_proxy_origin_request.arn
  handler       = "index.handler"
  runtime       = "nodejs10.x"
  s3_bucket     = aws_s3_bucket.static_lambda_proxy_production.id
  s3_key        = "current/origin-request-handler.zip"
  tags = {
    Environment = var.environment
  }
  depends_on = [aws_iam_role_policy_attachment.static_lambda_proxy_origin_request_logging,
  aws_cloudwatch_log_group.static_lambda_proxy_origin_request]
}

/*
 * Lambda@Edge Static Proxies - Origin Response
 */

resource "aws_iam_role" "static_lambda_proxy_origin_response" {
  name               = "static_lambda_proxy_origin_response"
  assume_role_policy = data.aws_iam_policy_document.static_lambda_execution_role.json
}

resource "aws_cloudwatch_log_group" "static_lambda_proxy_origin_response" {
  name              = "/aws/lambda/static-lambda-proxy-origin-response"
  retention_in_days = 14
}

resource "aws_iam_policy" "static_lambda_proxy_origin_response_logging" {
  name        = "static-lambda-proxy-origin-response-logging"
  path        = "/"
  description = "IAM policy for logging from a static lambda proxy for origin response"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${var.aws_region}:*:log-group:/aws/lambda/static-lambda-proxy-origin-response:*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "static_lambda_proxy_origin_response_logging" {
  role       = aws_iam_role.static_lambda_proxy_origin_response.name
  policy_arn = aws_iam_policy.static_lambda_proxy_origin_response_logging.arn
}

// TODO: there is no assurance lambda is on correct region
resource "aws_lambda_function" "static_lambda_proxy_origin_response" {
  function_name = "static-lambda-proxy-origin-response"
  role          = aws_iam_role.static_lambda_proxy_origin_response.arn
  handler       = "index.handler"
  runtime       = "nodejs10.x"
  s3_bucket     = aws_s3_bucket.static_lambda_proxy_production.id
  s3_key        = "current/origin-response-handler.zip"
  tags = {
    Environment = var.environment
  }
  depends_on = [aws_iam_role_policy_attachment.static_lambda_proxy_origin_response_logging,
  aws_cloudwatch_log_group.static_lambda_proxy_origin_response]
}


resource "aws_cloudfront_distribution" "docs_intercom" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = "custom.intercom.help"
    origin_id   = "INTERCOM_DOCS"
    custom_origin_config {
      http_port = "80"
      https_port = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols = ["TLSv1.2"]
    }
  }
  enabled             = true
  is_ipv6_enabled     = true
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = false
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "INTERCOM_DOCS"
    forwarded_values {
      headers = ["*"]
      query_string = true
      cookies {
        forward = "all"
      }
    }
  }
  aliases = ["docs.friday.ai"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.friday_certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }
  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }
  // FIXME quando tiver o recurso usar ele aqui
  web_acl_id              = "arn:aws:wafv2:us-east-1:************:global/webacl/WAF_Default_ACL/02b7ce70-1769-4b9c-b1f2-66f37a68ee9f"

  tags = {
    Environment = var.environment
  }
}