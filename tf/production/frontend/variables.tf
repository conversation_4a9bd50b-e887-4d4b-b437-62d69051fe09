variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "environment" {
  description = "The environment"
  default     = "production"
}

variable "certificate_arn" {
  description = "The arn for *.via1.app certificate"
  default = "arn:aws:acm:us-east-1:381563809177:certificate/f15fd077-674d-49c6-bda4-2298a934afc8"
}

variable "friday_certificate_arn" {
  description = "The arn for *.friday.ai certificate"
  default = "arn:aws:acm:us-east-1:381563809177:certificate/9e4015a8-1571-46d8-b034-57db1a9b6c4d"
}