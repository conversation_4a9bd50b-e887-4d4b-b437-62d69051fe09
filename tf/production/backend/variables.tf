variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "lambda_version" {
  default = "latest"
}

variable "s3_bucket" {
  default = "email-receiver"
}

variable "environment" {
  description = "The environment"
  default     = "production"
}

variable api_gateway_stage_name {
  type    = string
  default = "stage"
}

variable "nat_gateway_enabled" {
  description = "Enable nat gateway"
  default     = true
}

variable "celcoin_username" {
  description = "Celcoin Username"
  default     = null
}

variable "celcoin_password" {
  description = "Celcoin Password"
  default     = null
}

variable "celcoin_host" {
  default     = null
  description = "Celcoin host"
}

variable "cielo_merchant_id" {
  description = "Cielo Merchant Id"
  default     = null
}

variable "cielo_merchant_key" {
  description = "Cielo merchant Key"
  default     = null
}

variable "cielo_host" {
  description = "Cielo host"
  default     = null
}