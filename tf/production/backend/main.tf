locals {
  account_id                           = "************"
  production_availability_zones        = ["us-east-1a", "us-east-1b"]
  incoming_emails_sns_name             = "incoming-emails"
  incoming_emails_sqs_name             = "incoming-emails"
  bank_account_invalidation_sqs_name   = "bank-account-invalidation"
  bill_events_sns_name                 = "bill-events"
  wallet_events_sns_name               = "wallet-events"
  account_events_sns_name              = "account-events"
  bill_payment_scheduling_sqs_name     = "bill_payment_scheduling"
  ecs_sqs_list_policy_resource         = ["arn:aws:sqs:us-east-1:${local.account_id}:*"]
  user_documents_bucket_name           = "via1-user-documents"
  auth_secret                          = jsondecode(data.aws_secretsmanager_secret_version.auth_secret.secret_string)
  apple_secret                         = data.aws_secretsmanager_secret_version.auth_apple.secret_string
  dda_bills_lambda_s3_bucket           = "************-dda-files-lambda"
  multicom_files_bucket_name           = "multicom-files"
  facetec_bucket_name                  = "friday-facetec-documents"
  modatta_user_documents_bucket_arn    = "arn:aws:s3:::************-modatta-user-documents"
  connect_utility_errors_bucket_arn    = "arn:aws:s3:::connect-utility-errors"
  friday_dynamodb_exports_bucket_arn    = "arn:aws:s3:::friday-dynamodb-exports"
  firebase_delivery_failure_queue_name = "firebase-delivery-failure-queue"
  firebase_delivery_failure_topic_name = "firebase-delivery-failure-topic"
  config_files_bucket_name             = "${local.account_id}-config-files"
  prefix                               = "friday"
}

provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [local.account_id]
}

module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "stg-backend-infrastructure"
  dynamodb_table_name = "backend-infrastructure-lock-table"
}

module "ci" {
  source           = "../../modules/ci"
  environment      = var.environment
  account_id       = local.account_id
  cf_distributions = ["E3VPVN571BNGKT", "E3JM59S10UKNKZ", "E16P6VT2MLWGCD", "E2WPP7M1W9RN0K", "E16F46RTV8WO46"]
}

module "bill-payment" {
  source                          = "../../modules/bill-payment"
  public_bucket_name              = "via1-bill-payment-public"
  create_public_bucket            = true
  region                          = "us-east-1"
  user_receipts_bucket_name       = "bill-receipts"
  user_receipts_object_expiration = 7
}

module "sns" {
  source              = "../../modules/sns"
  monthly_spend_limit = 500
}

terraform {
  backend "s3" {
    bucket         = "stg-backend-infrastructure"
    key            = "stg/terraform.tfstate"
    dynamodb_table = "backend-infrastructure-lock-table"
    encrypt        = true
    region         = "us-east-1"
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.25"
    }
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.0.0"
  name    = "via1-vpc"
  cidr    = "10.0.0.0/16"

  azs             = ["us-east-1a", "us-east-1b"]
  public_subnets  = ["********/24", "********/24"]
  private_subnets = ["**********/24", "**********/24"]

  enable_nat_gateway   = var.nat_gateway_enabled
  single_nat_gateway   = true
  enable_vpn_gateway   = false
  enable_dns_hostnames = true

  tags = {
    Terraform   = "true"
    Environment = var.environment
  }
}

module "failure_rendering_notification" {
  source = "terraform-aws-modules/sns/aws"
  name   = "rendering-failed-emails"
}

module "ses-email-receiver" {
  source                                                = "../../modules/ses"
  ses_receiver_rule_name                                = "ses_receiver"
  email_domain                                          = ["via1.app", "mailbox.friday.ai"]
  scan_enabled                                          = true
  ses_bucket_name                                       = "ses-received-emails-via1"
  ses_unprocessed_emails_bucket_name                    = "ses-unprocessed-emails-via1"
  ses_unprocessed_emails_bucket_replication_enabled     = true
  ses_unprocessed_emails_bucket_replication_rule_id     = "ReplicationToAnotherAccountRule"
  ses_unprocessed_emails_bucket_replication_destination = "arn:aws:s3:::replica-ses-unprocessed-emails-via1"
  ses_unprocessed_emails_bucket_replication_role        = "arn:aws:iam::************:role/service-role/s3crr_role_for_ses-unprocessed-emails-via1"
  region                                                = var.aws_region
  quarantine_emails_bucket_name                         = "quarantine-emails"
  ses_incoming_emails_bucket_name                       = "via1-incoming-emails"
  ses_incoming_emails_bucket_replication_enabled        = true
  ses_incoming_emails_bucket_replication_rule_id        = "ReplicationToReplicaAccount"
  ses_incoming_emails_bucket_replication_destination    = "arn:aws:s3:::replica-via1-incoming-emails"
  ses_incoming_emails_bucket_replication_role           = "arn:aws:iam::************:role/service-role/s3crr_role_for_via1-incoming-emails"
  sns_failure_rendering_arn                             = module.failure_rendering_notification.topic_arn
  sns_receiver_emails_arn                               = module.incoming_emails.topic_arn
  notification_email_sender                             = "<EMAIL>"
}

module "incoming_emails" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.incoming_emails_sns_name
}

data "aws_iam_policy_document" "incoming-emails" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.incoming_emails_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = ["arn:aws:sns:${var.aws_region}:${local.account_id}:${local.incoming_emails_sns_name}"]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

module "incoming_emails_queue" {
  source         = "terraform-aws-modules/sqs/aws"
  version        = "~> 2.0"
  policy         = data.aws_iam_policy_document.incoming-emails.json
  name           = local.incoming_emails_sqs_name
  redrive_policy = "{\"deadLetterTargetArn\":\"${module.incoming_emails_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":1}"

  tags = {
    Environment = var.environment
  }
}

module "incoming_emails_dlq" {
  source                    = "terraform-aws-modules/sqs/aws"
  version                   = "~> 2.0"
  name                      = "${local.incoming_emails_sqs_name}-dlq"
  message_retention_seconds = 1209600
  tags = {
    Environment = var.environment
  }
}

resource "aws_sns_topic_subscription" "incoming_emails" {
  topic_arn            = module.incoming_emails.topic_arn
  protocol             = "sqs"
  endpoint             = module.incoming_emails_queue.this_sqs_queue_arn
  raw_message_delivery = true
}


resource "aws_dynamodb_table" "user_table" {
  name         = "Via1-BillPayment"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"
  deletion_protection_enabled = true

  ttl {
    attribute_name = "ExpirationTTL"
    enabled        = true
  }

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex3ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex4ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex3"
    hash_key        = "GSIndex3PrimaryKey"
    range_key       = "GSIndex3ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex4"
    hash_key        = "GSIndex4PrimaryKey"
    range_key       = "GSIndex4ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "event_table" {
  name         = "Via1-BillEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"
  deletion_protection_enabled = true

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "N"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}



resource "aws_dynamodb_table" "user_event_table" {
  name         = "Via1-UserEvents"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PrimaryKey"
  range_key    = "ScanKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PrimaryKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PrimaryKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PrimaryKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PrimaryKey"
    range_key       = "GSIndex2ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "dda_service_table" {
  name         = "Via1-DDA-Service"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "RangeKey"
  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1RangeKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_dynamodb_table" "liveness_gateway_events_table" {
  name         = "LivenessGateway-LivenessEvent"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "ScanKey"

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "N"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1ScanKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1ScanKey"
    projection_type = "ALL"
  }

  tags = {
    Environment = var.environment
    Project     = "Liveness"
  }
}

resource "aws_dynamodb_table" "server_lock_table" {
  name         = "Shedlock"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "_id"

  attribute {
    name = "_id"
    type = "S"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_security_group" "bill-payment-cache-cluster" {
  name        = "bill-payment-cache-cluster-sc"
  description = "bill-payment-cache-cluster"
  vpc_id      = module.vpc.vpc_id

  ingress {
    protocol        = "6"
    from_port       = "6379"
    to_port         = "6379"
    security_groups = [module.ecs_bill_payment_api.ecs_tasks_security_group_id]
  }
}

# FIXME comentado por conta de não saber o aumento de custo que isso vai causar. Acho que a gente deveria saber quais regras realmente vão fazer sentido
# visto que já temos o PITR
# resource "aws_backup_plan" "dynamodb_backup_plan" {
#   name = "Via1-BillEvents-AwsBackup"
#
#   rule {
#     rule_name         = "DynamoDBHourlySnapshotsRule"
#     target_vault_name = "Default"
#     schedule          = "cron(0 6/1 ? * * *)"
#     start_window      = 60
#     completion_window = 120
#
#     lifecycle {
#       delete_after = 3  # Retain backups for 3 days
#     }
#   }
#
#   rule {
#     rule_name         = "DynamoDBDailyBackupRule"
#     target_vault_name = "Default"
#     schedule          = "cron(10 6 ? * * *)"
#     start_window      = 480
#     completion_window = 1008
#
#     lifecycle {
#       delete_after = 8  # Retain backups for 35 days
#     }
#   }
#
#   rule {
#     rule_name         = "DynamoDBWeeklyBackupRule"
#     target_vault_name = "Default"
#     schedule          = "cron(20 6 ? * 7 *)"
#     start_window      = 480
#     completion_window = 10080
#
#     lifecycle {
#       delete_after = 35  # Retain backups for 35 days
#     }
#   }
#
#   rule {
#     rule_name         = "DynamoDBMonthlyBackupRule"
#     target_vault_name = "Default"
#     schedule          = "cron(30 6 1 * ? *)"
#     start_window      = 480
#     completion_window = 1440
#
#     lifecycle {
#       delete_after = 390  # Retain backups for 390 days
#     }
#   }
#
#   rule {
#     rule_name         = "DynamoDBYearlyBackupRule"
#     target_vault_name = "Default"
#     schedule          = "cron(40 6 2 1 ? *)"
#     start_window      = 480
#     completion_window = 2880
#
#     lifecycle {
#       delete_after = 2190  # Retain backups for 2190 days
#     }
#   }
# }

# resource "aws_backup_selection" "dynamodb_backup_selection" {
#   name          = "dybanodb-backup-selection"
#   iam_role_arn  = aws_iam_role.dynamodb_backup.arn
#   plan_id       = aws_backup_plan.dynamodb_backup_plan.id
#
#   resources = [
#     aws_dynamodb_table.user_table.arn,
#     aws_dynamodb_table.event_table.arn
#   ]
# }

# resource "aws_iam_role" "dynamodb_backup" {
#   name = "dynamodb-backup"
#
#   assume_role_policy = jsonencode({
#     Version = "2012-10-17",
#     Statement = [
#       {
#         Action = "sts:AssumeRole",
#         Effect = "Allow",
#         Principal = {
#           Service = "backup.amazonaws.com"
#         }
#       }
#     ]
#   })
# }

# resource "aws_iam_role_policy_attachment" "dynamodb_backup" {
#   role       = aws_iam_role.dynamodb_backup.name
#   policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup"
# }

resource "aws_elasticache_cluster" "bill-payment-cache-cluster" {
  cluster_id           = "bill-payment-cache"
  engine               = "redis"
  node_type            = "cache.t2.small"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  engine_version       = "7.0"
  subnet_group_name    = aws_elasticache_subnet_group.bill-payment-cache-cluster.name
  security_group_ids   = [aws_security_group.bill-payment-cache-cluster.id]

}

resource "aws_elasticache_subnet_group" "bill-payment-cache-cluster" {
  name       = "bill-payment-cache-subnet"
  subnet_ids = module.vpc.private_subnets
}

module "ecs_bill_payment_api" {
  source = "../../modules/ecs"

  ecr_repository_name                 = "bill-payment-api"
  ecr_repository_name_2               = "dda-bills"
  aws_private_subnet_id               = module.vpc.private_subnets
  aws_public_subnet_id                = module.vpc.public_subnets
  aws_vpc_id                          = module.vpc.vpc_id
  cluster_name                        = "bill-payment-cluster"
  service_name                        = "bill-payment-service"
  task_name                           = "bill-payment-task"
  alb_target_group_name               = "bill-payment-target-group"
  load_balance_name                   = "bill-payment-alb"
  app_port                            = 8443
  app_count                           = 1
  fargate_cpu                         = 4096
  fargate_memory                      = 8192
  health_check_path                   = "/health"
  task_definition                     = "${path.module}/task-definitions/service.json"
  certificate_arn                     = aws_acm_certificate.via1_certificate.arn
  alternative_certificate_arn_enabled = true
  alternative_certificate_arn         = aws_acm_certificate.friday_certificate.arn
  dynamo_access_enabled               = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.user_table.arn,
    "${aws_dynamodb_table.user_table.arn}/*",
    aws_dynamodb_table.event_table.arn,
    "${aws_dynamodb_table.event_table.arn}/*",
    "${aws_dynamodb_table.event_table.arn}/*",
    aws_dynamodb_table.user_event_table.arn,
    "${aws_dynamodb_table.user_event_table.arn}/*",
    aws_dynamodb_table.dda_service_table.arn,
    aws_dynamodb_table.server_lock_table.arn,
    "${aws_dynamodb_table.server_lock_table.arn}/*",
    module.open_finance.open_finance_data_table_arn,
    "${module.open_finance.open_finance_data_table_arn}/*",
  ]
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = local.ecs_sqs_list_policy_resource
  lambda_access_enabled   = true
  ecs_lambda_policy_resource = [
    module.dda_files_lambda.aws_lambda_function_arn
  ]
  ecs_task_role_name      = "bill-payment-task-role"
  secrets_enabled         = true
  sns_access_enabled      = true
  ecs_sns_policy_resource = [
    module.bill_events_sns.topic_arn,
    module.wallet_events_sns.topic_arn,
    module.account_events_sns.topic_arn,
    module.firebase_sns_platform_application.delivery_failure_topic.topic_arn,
    module.firebase_sns_platform_application.sns_platform_application.arn,
    "arn:aws:sns:${var.aws_region}:${local.account_id}:*"
  ]
  secrets_arns = [
    aws_secretsmanager_secret.software_express.arn,
    aws_secretsmanager_secret.celcoin_credentials.arn,
    aws_secretsmanager_secret.cielo_credentials.arn,
    aws_secretsmanager_secret.auth_secrets.arn,
    aws_secretsmanager_secret.blip_password.arn,
    aws_secretsmanager_secret.arbi_app_credentials.arn,
    aws_secretsmanager_secret.arbi_ecm_credentials.arn,
    aws_secretsmanager_secret.bigdatacorp_credentials.arn,
    aws_secretsmanager_secret.friday_intercom_credentials.arn,
    aws_secretsmanager_secret.userpilot.arn,
    aws_secretsmanager_secret.openai_key.arn,
    aws_secretsmanager_secret.via1_clearsale-credentials.arn,
    aws_secretsmanager_secret.vehicle_debts.arn,
    aws_secretsmanager_secret.via1_keystore.arn,
    aws_secretsmanager_secret.whatsapp_secrets.arn,
    /* CHECK "secrets_map" attribute instead of update task definition manually */
  ]
  secrets_map     = local.bill_payment_secrets_mapping
  s3_read_objects = true
  s3_bucket_arns = [
    module.ses-email-receiver.incoming_emails_bucket_arn,
    module.ses-email-receiver.email_receiver_s3_bucket_arn,
    module.ses-email-receiver.unprocessed_emails_bucket_arn,
    module.ses-email-receiver.quarantine_emails_bucket_arn,
    aws_s3_bucket.user_documents_bucket.arn,
    aws_s3_bucket.multicom_files_bucket.arn,
    module.bill-payment.user_receipts_bucket_arn,
    module.dda_files_lambda.aws_lambda_bucket_arn,
    local.modatta_user_documents_bucket_arn,
    local.connect_utility_errors_bucket_arn,
    local.friday_dynamodb_exports_bucket_arn,
  ]
  send_email              = true
  textract_enabled        = true
  access_logs_enabled     = true
  access_logs_bucket      = "new-bill-payment-alb"
  user_pool_arn           = module.via1_cognito.user_pool_arn
  app_version             = module.bill_payment_image_version.image_tag
  alb_target_group_name_2 = "dda-bills-target-group"
  app_version_2           = module.dda_service_image_version.image_tag
  service_name_2          = "dda-bills-service"
  task_definition_2       = "${path.module}/task-definitions/dda-service.json"
  task_name_2             = "dda-bills-task"
}

resource "aws_ecr_repository" "fluent_bit_ecr" {
  image_tag_mutability = "IMMUTABLE"
  name                 = "custom-fluent-bit"
}

module "ecs_liveness" {
  source = "../../modules/liveness"

  ecr_repository_name   = "liveness-api"
  aws_private_subnet_id = module.vpc.private_subnets
  aws_public_subnet_id  = module.vpc.public_subnets
  aws_vpc_id            = module.vpc.vpc_id
  cluster_name          = "liveness-cluster"
  service_name          = "liveness-service"
  task_name             = "liveness-gateway-task"
  alb_target_group_name = "liveness-target-group"
  load_balance_name     = "liveness-alb"
  app_port              = 8443
  app_count             = 1
  fargate_cpu           = 8192
  fargate_memory        = 15408
  health_check_path     = "/health"
  task_definition       = "${path.module}/task-definitions/liveness-gateway.json"
  certificate_arn       = aws_acm_certificate.friday_certificate.arn
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.liveness_gateway_events_table.arn,
    "${aws_dynamodb_table.liveness_gateway_events_table.arn}/*",
  ]
  ecs_task_execution_role_name = "liveness-task-execution-role"
  ecs_task_role_name           = "liveness-task-role"
  s3_read_objects              = true
  s3_bucket_arns               = [aws_s3_bucket.facetec-bucket.arn]
  secrets_enabled              = true
  secrets_arns = [
    aws_secretsmanager_secret.datadog_key.arn,
    aws_secretsmanager_secret.liveness_gateway_secrets.arn,
    aws_secretsmanager_secret.liveness_gateway_face-map-encryption-key.arn
  ]
  usage_logs_enabled = true
  usage_logs_secrets_arns = [
    aws_secretsmanager_secret.datadog_key.arn,
    aws_secretsmanager_secret.liveness_gateway_secrets.arn
  ]
  environment               = var.environment
  container_name            = "LivenessAPI"
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  sqs_access_enabled        = true
  ecs_sqs_policy_resource   = local.ecs_sqs_list_policy_resource
  tags = {
    Project = "Liveness"
  }
  use_fargate = false
}

module "ecs_settlement-service" {
  source = "../../modules/settlement-service"

  environment                  = var.environment
  ecr_repository_name          = "settlement-service-api"
  aws_private_subnet_id        = module.vpc.private_subnets
  aws_public_subnet_id         = module.vpc.public_subnets
  aws_vpc_id                   = module.vpc.vpc_id
  cluster_name                 = "settlement-service-cluster"
  service_name                 = "settlement-service"
  container_name               = "settlement-serviceAPI"
  task_name                    = "settlement-service-task"
  alb_target_group_name        = "settlement-service-target-group"
  load_balance_name            = "settlement-service-alb"
  app_port                     = 8443
  app_count                    = 2
  fargate_cpu                  = 512
  fargate_memory               = 2048
  health_check_path            = "/health"
  task_definition              = "${path.module}/task-definitions/settlement-service.json"
  certificate_arn              = aws_acm_certificate.friday_certificate.arn
  dynamo_access_enabled        = true
  ecs_task_execution_role_name = "settlement-service-task-execution-role"
  ecs_task_role_name           = "settlement-service-task-role"
  s3_read_objects              = false
  s3_bucket_arns               = []
  secrets_arns = [
    aws_secretsmanager_secret.datadog_key.arn,
    aws_secretsmanager_secret.celcoin_credentials.arn,
    aws_secretsmanager_secret.auth_secrets.arn,
    aws_secretsmanager_secret.arbi_app_credentials.arn
  ]
  fluent_bit_repository_url  = aws_ecr_repository.fluent_bit_ecr.repository_url
  sqs_access_enabled         = true
  ecs_sqs_policy_resource    = local.ecs_sqs_list_policy_resource
  settlement_events_sns_name = "settlement-events"
  tags = {
    Project = "settlement-service"
  }
  cloudwatch_alarms_enabled             = true
  cloudwatch_alarm_actions_P2_topic_arn = "arn:aws:sns:us-east-1:************:OpsgenieP2"
  cloudwatch_alarm_actions_P3_topic_arn = "arn:aws:sns:us-east-1:************:OpsgenieP3"
}

module "bill_payment_rollback_transaction_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  name                       = "bill_payment_rollback_transaction"
  delay_seconds              = 300
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 600
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_payment_rollback_transaction_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":144}"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_rollback_transaction_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_payment_rollback_transaction_dlq"

  tags = {
    Environment = var.environment
  }
}

module "bill_notification" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_notification"

  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_notification_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":144}"

  tags = {
    Environment = var.environment
  }
}

module "bill_notification_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bill_notification_dlq"

  tags = {
    Environment = var.environment
  }
}

resource "aws_acm_certificate" "via1_certificate" {
  domain_name               = "via1.app"
  subject_alternative_names = ["*.via1.app"]
  validation_method         = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate_validation" "via1_certificate" {
  certificate_arn = aws_acm_certificate.via1_certificate.arn
  validation_record_fqdns = [
    "_b2078e42b163a1ed42079424bbcb9075.via1.app"
  ]
}

resource "aws_acm_certificate" "friday_certificate" {
  domain_name               = "friday.ai"
  subject_alternative_names = ["*.friday.ai"]
  validation_method         = "DNS"
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate_validation" "friday_certificate" {
  certificate_arn = aws_acm_certificate.friday_certificate.arn
  validation_record_fqdns = [
    "_f22f05752634ba3408d251b9e7626fad.friday.ai"
  ]
}

resource "aws_secretsmanager_secret" "datadog_key" {
  name = "bill-payment-api/datadog"
}
resource "aws_secretsmanager_secret" "software_express" {
  name = "via1/software-express-credentials"
}
resource "aws_secretsmanager_secret" "celcoin_credentials" {
  name = "via1/celcoin-credentials"
}
resource "aws_secretsmanager_secret" "cielo_credentials" {
  name = "via1/cielo-credentials"
}
resource "aws_secretsmanager_secret" "auth_secrets" {
  name = "via1/auth-secrets"
}
resource "aws_secretsmanager_secret" "blip_password" {
  name = "bill-payment-api/blip-password"
}
resource "aws_secretsmanager_secret" "bigdatacorp_credentials" {
  name = "via1/bigdatacorp-credentials"
}
resource "aws_secretsmanager_secret" "arbi_app_credentials" {
  name = "via1/arbi-app-credentials"
}
resource "aws_secretsmanager_secret" "arbi_ecm_credentials" {
  name = "via1/arbi-ecm-credentials"
}
resource "aws_secretsmanager_secret" "friday_intercom_credentials" {
  name = "friday/intercom-credentials"
}
resource "aws_secretsmanager_secret" "auth_apple" {
  name = "via1/auth-apple"
}
resource "aws_secretsmanager_secret" "userpilot" {
  name = "friday/userpilot"
}
resource "aws_secretsmanager_secret" "openai_key" {
  name = "friday/openai_key"
}

resource "aws_secretsmanager_secret" "openai_daily_log_key" {
  name = "friday/openai_daily_log_key"
}

resource "aws_secretsmanager_secret" "vehicle_debts" {
  name = "friday/vehicle_debts"
}

resource "aws_secretsmanager_secret_version" "vehicle_debts" {
  secret_id = aws_secretsmanager_secret.vehicle_debts.id
  secret_string = jsonencode({
    "USERNAME" = "foo"
    "PASSWORD" = "foo"
  })
}

# resource "aws_secretsmanager_secret_version" "openai_daily_log_key" {
#   secret_id = aws_secretsmanager_secret.openai_daily_log_key.id
#     secret_string = ""
# }

resource "aws_secretsmanager_secret" "waba_token" {
  name = "friday/waba-token"
}

# resource "aws_secretsmanager_secret_version" "waba_token" {
#   secret_id = aws_secretsmanager_secret.waba_token.id
#     secret_string = ""
# }

resource "aws_secretsmanager_secret" "liveness_gateway_secrets" {
  name = "liveness-gateway/facetec"
  tags = {
    Project = "Liveness"
  }
}

resource "aws_secretsmanager_secret" "via1_clearsale-credentials" {
  name = "friday/clearsale-credentials"
  recovery_window_in_days = 7
}

resource "aws_secretsmanager_secret" "via1_keystore" {
  name = "friday/keystore"
  recovery_window_in_days = 7
}

resource "aws_secretsmanager_secret_version" "via1_keystore-first-value" {
  secret_id = aws_secretsmanager_secret.via1_keystore.id
  secret_string = jsonencode({
    "MASTERKEY"   = "foo"
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}


# resource "aws_secretsmanager_secret_version" "via1_clearsale-credentials-initialization" {
#   secret_id = aws_secretsmanager_secret.via1_clearsale-credentials.id
#   secret_string = jsonencode({
#     "HOST"        = "REPLACE_HOST"
#     "USERNAME"    = "REPLACE_USERNAME"
#     "PASSWORD"    = "REPLACE_PASSWORD"
#   })
#
#   lifecycle {
#     ignore_changes = [secret_string, version_stages]
#   }
# }

# TODO: ver quem tem acesso
resource "aws_secretsmanager_secret" "liveness_gateway_face-map-encryption-key" {
  name = "liveness-gateway/facetec/face-map-encryption-key"
  tags = {
    Project = "Liveness"
  }
}

resource "aws_secretsmanager_secret" "whatsapp_secrets" {
  name = "friday/whatsapp-secrets"
}

resource "aws_secretsmanager_secret_version" "whatsapp_secrets" {
  secret_id = aws_secretsmanager_secret.whatsapp_secrets.id
  secret_string = jsonencode({
    "SALT"       = "default_salt_value"
    "PRIVATE_KEY" = "default_privatekey_value"
  })
}

data "aws_iam_policy_document" "bank_account_policy" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.bank_account_invalidation_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = [module.bill_events_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

data "aws_iam_policy_document" "bill_payment_scheduling_queue_policy" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.bill_payment_scheduling_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = [module.bill_events_sns.topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

resource "aws_sns_topic_subscription" "bank_account_invalidation_queue_subscription" {
  topic_arn            = module.bill_events_sns.topic_arn
  protocol             = "sqs"
  endpoint             = module.bank_account_invalidation_queue.this_sqs_queue_arn
  filter_policy        = jsonencode({ "eventType" = tolist(["PAYMENT_FAIL", "PAYMENT_REFUNDED"]) })
  raw_message_delivery = true
}

resource "aws_sns_topic_subscription" "bill_payment_scheduling_queue_subscription" {
  topic_arn = module.bill_events_sns.topic_arn
  protocol  = "sqs"
  endpoint  = module.bill_payment_scheduling_queue.this_sqs_queue_arn
  filter_policy = jsonencode({
    "eventType" = tolist(["AMOUNT_UPDATED", "PAYMENT_SCHEDULED", "PAYMENT_SCHEDULE_CANCELED", "REGISTER_UPDATED"])
  })
  raw_message_delivery = true
}

module "bill_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.bill_events_sns_name
}

module "wallet_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.wallet_events_sns_name
}

module "account_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = local.account_events_sns_name
}

module "bank_account_invalidation_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.bank_account_policy.json
  name                       = local.bank_account_invalidation_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bank_account_invalidation_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":100}"

  tags = {
    Environment = var.environment
  }
}

module "bank_account_invalidation_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "bank-account-invalidation-dlq"

  tags = {
    Environment = var.environment
  }
}


module "bill_payment_scheduling_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.bill_payment_scheduling_queue_policy.json
  name                       = local.bill_payment_scheduling_sqs_name
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.bill_payment_scheduling_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":60}"

  tags = {
    Environment = var.environment
  }
}

module "bill_payment_scheduling_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "${local.bill_payment_scheduling_sqs_name}_dlq"

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "user_documents_bucket" {
  bucket = local.user_documents_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = true
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  replication_configuration {
    role = "arn:aws:iam::************:role/service-role/s3crr_role_for_via1-user-documents"
    //TODO trazer a role para o terraform
    rules {
      id       = "ReplicationToReplicaAccountRule"
      priority = 0
      status   = "Enabled"
      destination {
        account_id = "************"                             //TODO variavel?
        bucket     = "arn:aws:s3:::replica-via1-user-documents" //TODO trazer o bucket para o terraform
        access_control_translation {
          owner = "Destination"
        }
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "multicom_files_bucket" {
  bucket = local.multicom_files_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = false
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "facetec-bucket" {
  bucket = local.facetec_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  versioning {
    enabled = false
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Environment = var.environment
  }
}

data "aws_secretsmanager_secret_version" "auth_secret" {
  secret_id = aws_secretsmanager_secret.auth_secrets.id
}

data "aws_secretsmanager_secret_version" "auth_apple" {
  secret_id = aws_secretsmanager_secret.auth_apple.id
}

module "via1_cognito" {
  source                           = "../../modules/cognito"
  user_pool_name                   = "via1-user-pool"
  user_pool_domain_name            = "auth.via1.app"
  user_pool_domain_certificate_arn = aws_acm_certificate.via1_certificate.arn
  user_pool_domain_zone_id         = "Z2CBI6KP0EFGTD"
  provider_google_app_id           = "96251380574-lvk3pfvtsqjhb75lesb974niah1pmdhs.apps.googleusercontent.com"
  provider_google_app_secret       = local.auth_secret.GOOGLE_SECRET
  notification_email_sender_arn    = aws_ses_email_identity.notificacoes_email_address.arn
  provider_apple_client_id         = "app.via1.webapp"
  provider_apple_team_id           = "AJ6L79GVKW"
  provider_apple_key_id            = "27XJ663CLN"
  provider_apple_private_key       = local.apple_secret
  access_token_validity            = 60
  id_token_validity                = 60
  callback_urls = [
    "https://use.via1.app/autenticacao-federado", "https://use.friday.ai/autenticacao-federado"
  ]
  logout_urls = ["https://use.via1.app/", "https://use.friday.ai/"]
  prefix = local.prefix
  environment = var.environment
  datadog_key_arn = aws_secretsmanager_secret.datadog_key.arn
  lambda_config_enabled = true
}

resource "aws_ses_email_identity" "notificacoes_email_address" {
  email = "<EMAIL>"
}

resource "aws_ses_email_identity" "comprovantes_email_address" {
  email = "<EMAIL>"
}

module "dda_files_lambda" {
  source                = "../../modules/lambda"
  environment           = "production"
  lambda_runtime        = "python3.9"
  lambda_filename       = "../../files/dda-files-lambda.zip"
  lambda_handler        = "lambda_function.lambda_handler"
  lambda_iam_role_name  = "dda-files-lambda-role"
  lambda_name           = "dda-files"
  s3_read_write_objects = true
  lambda_s3_bucket      = "************-dda-files-lambda"
  lambda_version        = "1"
  lambda_memory_size    = 1024
  lambda_timeout        = 900
  environment_variables = {
    ARBI_FTP_HOST     = jsondecode(data.aws_secretsmanager_secret_version.arbi_ftp.secret_string)["host"]
    ARBI_FTP_PORT     = jsondecode(data.aws_secretsmanager_secret_version.arbi_ftp.secret_string)["port"]
    ARBI_FTP_USERNAME = jsondecode(data.aws_secretsmanager_secret_version.arbi_ftp.secret_string)["username"]
    ARBI_FTP_PASSWORD = jsondecode(data.aws_secretsmanager_secret_version.arbi_ftp.secret_string)["password"]
    FTP_ROOT_FOLDER   = "/Prod/DDAFiles_34701685000115"
    BUCKET_NAME       = local.dda_bills_lambda_s3_bucket
    TZ                = "Brazil/East"
  }
}

resource "aws_secretsmanager_secret" "arbi_ftp" {
  name = "friday/arbi-ftp"
}

data "aws_secretsmanager_secret_version" "arbi_ftp" {
  secret_id = aws_secretsmanager_secret.arbi_ftp.id
}

module "firebase_sns_platform_application" {
  source                      = "../../modules/sns-platform-application"
  delivery_failure_queue_arn  = "arn:aws:sqs:${var.aws_region}:${local.account_id}:${local.firebase_delivery_failure_queue_name}"
  delivery_failure_queue_name = local.firebase_delivery_failure_queue_name
  delivery_failure_topic_name = local.firebase_delivery_failure_topic_name
  sns_platform_application    = "firebase_sns_platform_application"
}

module "dda_service_image_version" {
  source         = "../../modules/image_version"
  container_name = "DDABills"
  service_name   = "dda-bills-service"
}

module "bill_payment_image_version" {
  source         = "../../modules/image_version"
  container_name = "BillPaymentAPI"
  service_name   = "bill-payment-service"
}

// FIXME remover quando migrar para os modulos novos
data "aws_iam_policy_document" "kms" {
  statement {
    actions = [
      "kms:Encrypt",
      "kms:Decrypt"
    ]
    resources = [aws_kms_key.connect_utility_key.arn, module.open_finance.open_finance_key_arn]

  }
}

resource "aws_iam_policy" "kms" {
  description = "Allow encrypt and decrypt data"

  policy = data.aws_iam_policy_document.kms.json
}

resource "aws_iam_role_policy_attachment" "kms" {
  role       = module.ecs_bill_payment_api.bill_payment_task_role.name
  policy_arn = aws_iam_policy.kms.arn
}

moved {
  from = module.ecs_bill_payment_api.aws_ecr_repository.fluent_bit_ecr
  to   = aws_ecr_repository.fluent_bit_ecr
}

resource "aws_kms_key" "connect_utility_key" {
  description             = "KMS key for bill payment encryption"
  deletion_window_in_days = 10
  multi_region            = true
}

resource "aws_kms_alias" "connect_utility_key" {
  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_key.connect_utility_key.key_id
}

provider "aws" {
  alias  = "replica"
  region = "sa-east-1"
}

resource "aws_kms_replica_key" "connect_utility_key_replica" {
  provider = aws.replica

  description             = "KMS key for bill payment encryption replica"
  deletion_window_in_days = 10
  primary_key_arn         = aws_kms_key.connect_utility_key.arn
}

resource "aws_kms_alias" "connect_utility_key_replica" {
  provider = aws.replica

  name          = "alias/utility-connect-key"
  target_key_id = aws_kms_replica_key.connect_utility_key_replica.key_id
}

module "ai_chatbot" {
  source = "../../modules/ai-chatbot"

  aws_region                      = "us-east-1"
  environment                     = var.environment
  task_definition                 = "${path.module}/task-definitions/ai-chatbot.json"
  fluent_bit_repository_url       = aws_ecr_repository.fluent_bit_ecr.repository_url
  private_subnets                 = module.vpc.private_subnets
  public_subnets                  = module.vpc.public_subnets
  vpc_id                          = module.vpc.vpc_id
  certificate_arn                 = aws_acm_certificate.friday_certificate.arn
  ecs_sqs_list_policy_resource    = local.ecs_sqs_list_policy_resource
  shedlock_table_arn              = aws_dynamodb_table.server_lock_table.arn
  billpayment_table_arn           = aws_dynamodb_table.user_table.arn
  secrets_arns = {
    "OPENAI_TOKEN" = aws_secretsmanager_secret.openai_key.arn,
    "OPENAI_DAILY_LOG_TOKEN" = aws_secretsmanager_secret.openai_daily_log_key.arn,
    "INTEGRATIONS_WHATSAPP_API_TOKEN" = aws_secretsmanager_secret.waba_token.arn,
    "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH" = aws_secretsmanager_secret.blip_password.arn,
  }
  s3_bucket_arns = [module.wa_comm_centre.wa_comm_centre_bucket_arn, local.friday_dynamodb_exports_bucket_arn]
  cognito_token_sns_topic_arn = module.via1_cognito.cognito_token_sns_topic_arn
  cognito_custom_sender_kms_key_arn = module.via1_cognito.cognito_custom_sender_kms_key_arn
}

module "wa_comm_centre" {
  source = "../../modules/wa-comm-centre"

  aws_region                = "us-east-1"
  environment               = var.environment
  task_definition           = "${path.module}/task-definitions/wa-comm-centre.json"
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  private_subnets           = module.vpc.private_subnets
  public_subnets            = module.vpc.public_subnets
  vpc_id                    = module.vpc.vpc_id
  certificate_arn           = aws_acm_certificate.friday_certificate.arn
  ecs_sqs_policy_resource   = local.ecs_sqs_list_policy_resource
  wa_comm_centre_bucket_name = "${local.account_id}-wa-comm-centre"
  mtls_certificate_bucket    = aws_s3_bucket.certificates.bucket
  mtls_certificate_key       = aws_s3_object.meta_intermediate_cert_pem.key
}

module "open_finance" {
  source = "../../modules/open-finance"

  aws_region                = "us-east-1"
  environment               = var.environment
  task_definition           = "${path.module}/task-definitions/open-finance.json"
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  private_subnets           = module.vpc.private_subnets
  public_subnets            = module.vpc.public_subnets
  vpc_id                    = module.vpc.vpc_id
  certificate_arn           = aws_acm_certificate.friday_certificate.arn
  client_task_role_arn      = module.ecs_bill_payment_api.bill_payment_task_role_arn
  account_id                = local.account_id
  fargate_cpu               = 1024
  fargate_memory            = 4096
}

resource "aws_vpc_endpoint" "ecr_api" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.aws_region}.ecr.api"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true

  security_group_ids = [
    module.open_finance.alb_security_group_id,
    module.ecs_bill_payment_api.alb_security_group_id,
    module.ai_chatbot.alb_security_group_id,
    module.ecs_settlement-service.alb_security_group_id,
    module.ecs_liveness.alb_security_group_id]
  subnet_ids         = module.vpc.private_subnets
}

resource "aws_vpc_endpoint" "s3" {
  vpc_id            = module.vpc.vpc_id
  service_name      = "com.amazonaws.${var.aws_region}.s3"
  vpc_endpoint_type = "Gateway"

  route_table_ids = module.vpc.private_route_table_ids
}

resource "aws_s3_bucket" "fluent_bit_configs" {
  bucket = local.config_files_bucket_name
}

resource "aws_s3_bucket" "certificates" {
  bucket = "${local.account_id}-certificates"

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}

resource "aws_s3_bucket_policy" "certificates" {
  bucket = aws_s3_bucket.certificates.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowALBServiceRole"
        Effect    = "Allow"
        Principal = {
          Service = "elasticloadbalancing.amazonaws.com"
        }
        Action = [
          "s3:GetObject"
        ]
        Resource = [
          "${aws_s3_bucket.certificates.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_object" "meta_intermediate_cert" {
  bucket = aws_s3_bucket.certificates.bucket
  key    = "meta/DigiCertSHA2HighAssuranceServerCA.crt"
  source = "${path.module}/certs/DigiCertSHA2HighAssuranceServerCA.crt"
  acl    = "private"
  etag   = filemd5("${path.module}/certs/DigiCertSHA2HighAssuranceServerCA.crt")
}

resource "aws_s3_object" "meta_intermediate_cert_pem" {
  bucket = aws_s3_bucket.certificates.bucket
  key    = "meta/DigiCertSHA2HighAssuranceServerCA.crt.pem"
  source = "${path.module}/certs/DigiCertSHA2HighAssuranceServerCA.crt.pem"
  acl    = "private"
  etag   = filemd5("${path.module}/certs/DigiCertSHA2HighAssuranceServerCA.crt.pem")
}

resource "aws_s3_object" "fluent_bit_general_conf" {
  bucket = aws_s3_bucket.fluent_bit_configs.bucket
  key    = "config/general.conf"
  source = "../../files/fluent-bit/general.conf"
  acl    = "private"
}

resource "aws_s3_object" "fluent_bit_parsers_conf" {
  bucket = aws_s3_bucket.fluent_bit_configs.bucket
  key    = "config/parsers.conf"
  source = "../../files/fluent-bit/parsers.conf"
  acl    = "private"
}

module "debitos_veiculares" {
  source                    = "../../modules/debitos-veiculares"
  environment               = var.environment
  cluster                   = module.ecs_bill_payment_api.cluster
  aws_region                = var.aws_region
  account_id                = local.account_id
  vpc_id                    = module.vpc.vpc_id
  private_subnets           = module.vpc.private_subnets
  public_subnets            = module.vpc.public_subnets
  fargate_cpu               = 1024
  fargate_memory            = 2048
  certificate_arn           = aws_acm_certificate.friday_certificate.arn
  s3_bucket_arns            = [
    aws_s3_bucket.fluent_bit_configs.arn
  ]
  secrets                   = [
    aws_secretsmanager_secret.datadog_key.arn,
  ]
}

