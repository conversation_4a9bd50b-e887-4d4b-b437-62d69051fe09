[{"name": "Via2Service", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/via2-service:${app_version}", "cpu": 502, "memory": 974, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "production"}, {"name": "JAVA_OPTS", "value": "-Xmx974m"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "via2-service"}], "secrets": [{"name": "WHATSAPP_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/whatsapp_key-DYQrMz:TOKEN::"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "via2-service"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "us-east-1", "log_group_name": "/ecs/via2-task", "auto_create_group": "false", "log_stream_name": "ecs/via2-task/$(ecs_task_id)"}}}, {"name": "datadog-agent", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1", "cpu": 10, "memory": 50, "essential": true, "environment": [{"name": "DD_API_KEY", "value": "********************************"}, {"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/via2-task-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}, {"image": "${fluent_bit_repository_url}:2.3", "name": "log_router", "essential": true, "cpu": 0, "portMappings": [], "user": "0", "volumesFrom": [], "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "environment": [{"name": "SERVICE_CONTAINER_NAME", "value": "Via2Service"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "via2-service"}, {"name": "LOG_GROUP_NAME", "value": "/logs/via2-task"}, {"name": "DD_API_KEY", "value": "********************************"}, {"name": "REGION", "value": "us-east-1"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/via2-task-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 50}]