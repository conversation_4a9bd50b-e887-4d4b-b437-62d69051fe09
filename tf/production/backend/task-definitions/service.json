[
  {
    "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/bill-payment-api:${app_version}",
    "cpu": 4045,
    "memory": 7936,
    "essential": true,
    "stopTimeout": 120,
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "friday,production"
      },
      {
        "name": "JAVA_OPTS",
        "value": "-Xmx4096m"
      },
      {
        "name": "DD_ENV",
        "value": "production"
      },{
        "name": "DD_SERVICE",
        "value": "bill-payment-service"
      },
      {
        "name": "DD_VERSION",
        "value": "${app_version}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "production",
      "com.datadoghq.tags.service": "bill-payment-service",
      "com.datadoghq.tags.version": "${app_version}"
    },
    "secrets": [
      %{ for env, value in secrets_map }
      {
        "name": "${env}",
        "valueFrom": "${value}"
      },
      %{ endfor ~}
      {
        "name": "INTEGRATIONS_CELCOIN_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:USERNAME::"
      },
      {
        "name": "INTEGRATIONS_CELCOIN_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_CELCOIN_TOKEN_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:ID::"
      },
      {
        "name": "INTEGRATIONS_CELCOIN_TOKEN_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:SECRET::"
      },
      {
        "name": "MICRONAUT_HTTP_SERVICES_CELCOIN_SSL_KEY_STORE_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/celcoin-credentials-Ys6Gbc:MTLS_P12_PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_CIELO_CREDENTIALS_MERCHANT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/cielo-credentials-3wnxre:MERCHANT_ID::"
      },
      {
        "name": "INTEGRATIONS_CIELO_CREDENTIALS_MERCHANT_KEY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/cielo-credentials-3wnxre:MERCHANT_KEY::"
      },
      {
        "name": "BILL_PAYMENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:JWT_SECRET::"
      },
      {
        "name": "GOOGLE_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:GOOGLE_SECRET::"
      },
      {
        "name": "APPLE_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:APPLE_SECRET::"
      },
      {
        "name": "INTEGRATIONS_SERPRO_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:SERPRO_USERNAME::"
      },
      {
        "name": "INTEGRATIONS_SERPRO_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:SERPRO_PASSWORD::"
      },
      {
        "name": "CELCOIN_CALLBACK_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:CELCOIN_SECRET::"
      },
      {
        "name": "ARBI_CALLBACK_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:ARBI_SECRET::"
      },
      {
        "name": "INTEGRATIONS_REVENUE_CAT_SECRET_KEY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:REVENUE_CAT_SECRET_KEY::"
      },
      {
        "name": "INTEGRATIONS_REVENUE_CAT_SECRET_KEY_V1",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:REVENUE_CAT_SECRET_KEY_V1::"
      },
      {
        "name": "REVENUE_CAT_CALLBACK_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:REVENUE_CAT_CALLBACK_SECRET::"
      },
      {
        "name": "INTEGRATIONS_QUOD_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:QUOD_USERNAME::"
      },
      {
        "name": "INTEGRATIONS_QUOD_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:QUOD_PASSWORD::"
      },
      {
        "name": "MODATTA_B2B_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:MODATTA_B2B_SECRET::"
      },
      {
        "name": "INTEGRATIONS_FIREBASE_CLOUD_MESSAGING_JSON",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:FIREBASE_CLOUD_MESSAGING_JSON::"
      },
      {
        "name": "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:bill-payment-api/blip-password-0qcJeR"
      },
      {
        "name": "INTEGRATIONS_ARBI_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:CLIENT_SECRET::"
      },
      {
        "name": "INTEGRATIONS_ARBI_CLIENT_ID",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:CLIENT_ID::"
      },
      {
        "name": "INTEGRATIONS_ARBI_USER_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-app-credentials-JR23mE:USER_TOKEN::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_CLIENT",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-ecm-credentials-NDGxcz:CLIENT::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_API_KEY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-ecm-credentials-NDGxcz:API_KEY::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-ecm-credentials-NDGxcz:USERNAME::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-ecm-credentials-NDGxcz:PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_ARBI_FEPWEB_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-ecm-credentials-NDGxcz:FEPWEB_USERNAME::"
      },
      {
        "name": "INTEGRATIONS_ARBI_FEPWEB_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/arbi-ecm-credentials-NDGxcz:FEPWEB_PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_INTERCOM_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/intercom-credentials-dIyK4a:TOKEN::"
      },
      {
        "name": "INTEGRATIONS_INTERCOM_WEB_ID_VERIFICATION_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/intercom-credentials-dIyK4a:WEB::"
      },
      {
        "name": "INTEGRATIONS_INTERCOM_IOS_ID_VERIFICATION_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/intercom-credentials-dIyK4a:IOS::"
      },
      {
        "name": "INTEGRATIONS_INTERCOM_ANDROID_ID_VERIFICATION_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/intercom-credentials-dIyK4a:ANDROID::"
      },
      {
        "name": "INTEGRATIONS_BIGDATACORP_ACCESS_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/bigdatacorp-credentials-m3n0SZ:ACCESS_TOKEN::"
      },
      {
        "name": "INTEGRATIONS_BIGDATACORP_LOGIN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/bigdatacorp-credentials-m3n0SZ:LOGIN::"
      },
      {
        "name": "INTEGRATIONS_BIGDATACORP_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/bigdatacorp-credentials-m3n0SZ:PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_USERPILOT_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/userpilot-tQIhUV:TOKEN::"
      },
      {
        "name": "INTEGRATIONS_SETTLEMENT_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:SETTLEMENT_SERVICE_CLIENT_IDENTITY::"
      },
      {
        "name": "INTEGRATIONS_SETTLEMENT_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:SETTLEMENT_SERVICE_CLIENT_SECRET::"
      },
      {
        "name": "KMS_HMAC_KEY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/cielo-credentials-3wnxre:HMAC_KEY::"
      },
      {
        "name": "MICRONAUT_HTTP_SERVICES_ARBI_SSL_KEY_STORE_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:via1/auth-secrets-Nu9sEd:ARBI_MTLS_PASSWORD::"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8443,
        "hostPort": 8443
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver":"awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/bill-payment-task",
        "auto_create_group": "false",
        "log_stream_name": "ecs/bill-payment-task/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 51,
    "memory": 256,
    "essential": true,
    "environment" : [
      {
        "name": "DD_API_KEY",
        "value": "********************************"
      },
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/bill-payment-task",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "essential": true,
    "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/custom-fluent-bit:1.3",
    "name": "log_router",
    "cpu":0,
    "portMappings"          : [],
    "user"                  : "0",
    "volumesFrom"           : [],
    "firelensConfiguration": {
        "type": "fluentbit",
        "options": {
          "config-file-type": "file",
          "config-file-value": "/bill-payment-service.conf"
        }
    },
    "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
            "awslogs-group": "/ecs/bill-payment-task",
            "awslogs-region": "us-east-1",
            "awslogs-stream-prefix": "ecs"
        }
    },
    "memoryReservation": 50
  }
]
