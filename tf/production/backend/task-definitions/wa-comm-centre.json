[{"name": "WACommCentre", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/wa-comm-centre:${app_version}", "cpu": 1014, "memory": 1742, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "production"}, {"name": "JAVA_OPTS", "value": "-Xmx1048m"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "wa-comm-centre"}], "secrets": [{"name": "WHATSAPP_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/whatsapp_key-DYQrMz:TOKEN::"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "wa-comm-centre"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "us-east-1", "log_group_name": "/ecs/wa-comm-centre-task", "auto_create_group": "false", "log_stream_name": "ecs/wa-comm-centre-task/$(ecs_task_id)"}}}, {"name": "datadog-agent", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1", "cpu": 10, "memory": 256, "essential": true, "environment": [{"name": "DD_API_KEY", "value": "********************************"}, {"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/wa-comm-centre-task-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}, {"image": "${fluent_bit_repository_url}:2.3", "name": "log_router", "essential": true, "cpu": 0, "portMappings": [], "user": "0", "volumesFrom": [], "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "environment": [{"name": "SERVICE_CONTAINER_NAME", "value": "WACommCentre"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "wa-comm-centre"}, {"name": "LOG_GROUP_NAME", "value": "/logs/wa-comm-centre-task"}, {"name": "DD_API_KEY", "value": "********************************"}, {"name": "REGION", "value": "us-east-1"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/wa-comm-centre-task-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 50}]