[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": 8172,
    "memory": 14896,
    "essential": true,
    "mountPoints": [
      {
        "sourceVolume": "liveness-gateway-efs-volume",
        "containerPath": "/search3d3d"
      }
    ],
    "environment": [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "${environment}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "INTEGRATIONS_FACETEC_SDK_USAGE_LOGS_SERVER_URI",
        "value": "${usage_logs_server_url}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "secrets": [
      {
        "name": "INTEGRATIONS_FACETEC_SDK_DEVICE_KEY_IDENTIFIER",
        "valueFrom": "${secret_arns[1]}:DEVICE_KEY_IDENTIFIER::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_SDK_SERVER_KEY",
        "valueFrom": "${secret_arns[1]}:SERVER_KEY::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_SDK_PRODUCTION_KEY_TEXT",
        "valueFrom": "${secret_arns[1]}:PRODUCTION_KEY_TEXT::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_CLIENT_PRODUCTION_KEY_TEXT",
        "valueFrom": "${secret_arns[1]}:CLIENT_PRODUCTION_KEY_TEXT::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_CLIENT_DEVICE_KEY_IDENTIFIER",
        "valueFrom": "${secret_arns[1]}:DEVICE_KEY_IDENTIFIER::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_CLIENT_PUBLIC_FACE_SCAN_ENCRYPTION_KEY",
        "valueFrom": "${secret_arns[1]}:PUBLIC_FACE_SCAN_ENCRYPTION_KEY::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_SDK_FACE_MAP_ENCRYPTION_KEY",
        "valueFrom": "${secret_arns[2]}:FACE_MAP_ENCRYPTION_KEY::"
      }
    ],
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port}
      },
      {
        "containerPort": 2049,
        "hostPort": 2049
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver": "awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${service_name}-task",
        "auto_create_group": "false",
        "log_stream_name": "ecs/${service_name}-task/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment": [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "DD_PROCESS_AGENT_ENABLED",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      },
      {
        "hostPort": 8126,
        "protocol": "tcp",
        "containerPort": 8126
      }
    ],
    "mountPoints": [
      {
        "containerPath": "/var/run/docker.sock",
        "sourceVolume": "docker_sock",
        "readOnly": null
      },
      {
        "containerPath": "/host/sys/fs/cgroup",
        "sourceVolume": "cgroup",
        "readOnly": null
      },
      {
        "containerPath": "/host/proc",
        "sourceVolume": "proc",
        "readOnly": null
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${service_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "image": "${fluent_bit_repository_url}:2.3",
    "name": "log_router",
    "essential": true,
    "cpu": 0,
    "portMappings": [],
    "user": "0",
    "volumesFrom": [],
    "firelensConfiguration": {
      "type": "fluentbit",
      "options": {
        "config-file-type": "file",
        "config-file-value": "/general/general.conf"
      }
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment": [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "${container_name}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/${service_name}-task"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${service_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50
  }
]
