[{"name": "OpenFinance", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/open-finance:${app_version}", "cpu": 1014, "memory": 3789, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "production"}, {"name": "JAVA_OPTS", "value": "-Xmx2048m"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "open-finance"}], "secrets": [{"name": "INTEGRATIONS_INICIADOR_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/iniciador-olGe8d:CLIENT_ID::"}, {"name": "INTEGRATIONS_INICIADOR_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/iniciador-olGe8d:CLIENT_SECRET::"}, {"name": "INTEGRATIONS_INICIADOR_SWEEPING_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/iniciador-olGe8d:SWEEPING_CLIENT_ID::"}, {"name": "INTEGRATIONS_INICIADOR_SWEEPING_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:381563809177:secret:friday/iniciador-olGe8d:SWEEPING_CLIENT_SECRET::"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "open-finance"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "us-east-1", "log_group_name": "/ecs/open-finance-task", "auto_create_group": "false", "log_stream_name": "ecs/open-finance-task/$(ecs_task_id)"}}}, {"name": "datadog-agent", "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1", "cpu": 10, "memory": 256, "essential": true, "environment": [{"name": "DD_API_KEY", "value": "********************************"}, {"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/open-finance-task-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}, {"image": "${fluent_bit_repository_url}:2.3", "name": "log_router", "essential": true, "cpu": 0, "portMappings": [], "user": "0", "volumesFrom": [], "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "environment": [{"name": "SERVICE_CONTAINER_NAME", "value": "OpenFinance"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "open-finance"}, {"name": "LOG_GROUP_NAME", "value": "/logs/open-finance-task"}, {"name": "DD_API_KEY", "value": "********************************"}, {"name": "REGION", "value": "us-east-1"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/open-finance-task-dd", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 50}]