locals {
  bill_payment_secrets_mapping = {
    "INTEGRATIONS_CLEARSALE_HOST"                            = "${aws_secretsmanager_secret.via1_clearsale-credentials.arn}:HOST::"
    "INTEGRATIONS_CLEARSALE_USERNAME"                        = "${aws_secretsmanager_secret.via1_clearsale-credentials.arn}:USERNAME::"
    "INTEGRATIONS_CLEARSALE_PASSWORD"                        = "${aws_secretsmanager_secret.via1_clearsale-credentials.arn}:PASSWORD::"
    "INTEGRATIONS_SOFTWARE_EXPRESS_CREDENTIALS_MERCHANT_ID"  = "${aws_secretsmanager_secret.software_express.arn}:MERCHANT_ID::"
    "INTEGRATIONS_SOFTWARE_EXPRESS_CREDENTIALS_MERCHANT_KEY" = "${aws_secretsmanager_secret.software_express.arn}:MERCHANT_KEY::"
    "INTEGRATIONS_VEHICLE_DEBTS_USERNAME"                    = "${aws_secretsmanager_secret.vehicle_debts.arn}:USERNAME::"
    "INTEGRATIONS_VEHICLE_DEBTS_PASSWORD"                    = "${aws_secretsmanager_secret.vehicle_debts.arn}:PASSWORD::"
    "TENANT_ENCRYPTION_MASTERKEY"                            = "${aws_secretsmanager_secret.via1_keystore.arn}:MASTERKEY::"
    "WHATSAPPFLOW_SALT"                                      = "${aws_secretsmanager_secret.whatsapp_secrets.arn}:SALT::"
    "WHATSAPPFLOW_PRIVATE_KEY"                                = "${aws_secretsmanager_secret.whatsapp_secrets.arn}:PRIVATE_KEY::"
  }
}

