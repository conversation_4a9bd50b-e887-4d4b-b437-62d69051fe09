variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "sa-east-1"
}

variable "environment" {
  description = "The environment"
  default     = "staging"
}

variable "api_gateway_stage_name" {
  type    = string
  default = "stage"
}

variable "nat_gateway_enabled" {
  description = "Enable nat gateway"
  default     = true
}

variable "connect_utility_replica_key_arn" {
  description = "Arn of the connect utility replica key created on sa-east-1 account"
  default = "arn:aws:kms:sa-east-1:************:key/mrk-6f37532a92e14d42bc4dc632e5e80728"
}