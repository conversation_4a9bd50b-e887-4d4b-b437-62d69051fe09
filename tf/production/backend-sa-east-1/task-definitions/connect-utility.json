[{"name": "ConnectUtility", "image": "381563809177.dkr.ecr.sa-east-1.amazonaws.com/connect-utility:${app_version}", "cpu": 0, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "production"}, {"name": "JAVA_OPTS", "value": "-Xmx2048m"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "connect-utility-service"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "connect-utility"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "sa-east-1", "log_group_name": "/ecs/connect-utility-task", "auto_create_group": "false", "log_stream_name": "ecs/connect-utility-task/$(ecs_task_id)"}}}, {"name": "ConnectUtilityOcr", "image": "381563809177.dkr.ecr.sa-east-1.amazonaws.com/connect-utility-ocr:${app_version_2}", "cpu": 0, "essential": true, "environment": [{"name": "PORT", "value": "8037"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "connect-utility-service"}, "portMappings": [{"containerPort": 8037}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "sa-east-1", "log_group_name": "/ecs/connect-utility-task", "auto_create_group": "false", "log_stream_name": "ecs/connect-utility-task/$(ecs_task_id)"}}}, {"name": "ConnectUtilitySpeech2Text", "image": "381563809177.dkr.ecr.sa-east-1.amazonaws.com/connect-utility-s2t:${app_version_3}", "cpu": 0, "essential": true, "environment": [{"name": "PORT", "value": "8029"}], "dockerLabels": {"com.datadoghq.tags.env": "production", "com.datadoghq.tags.service": "connect-utility-service"}, "portMappings": [{"containerPort": 8029}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "cloudwatch", "region": "sa-east-1", "log_group_name": "/ecs/connect-utility-task", "auto_create_group": "false", "log_stream_name": "ecs/connect-utility-task/$(ecs_task_id)"}}}, {"name": "datadog-agent", "image": "datadog/agent:latest", "cpu": 0, "essential": true, "environment": [{"name": "DD_API_KEY", "value": "********************************"}, {"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "true"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/connect-utility-task-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}, {"image": "${fluent_bit_repository_url}:2.4", "name": "log_router", "essential": true, "cpu": 0, "portMappings": [], "user": "0", "volumesFrom": [], "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/general/general.conf"}}, "environment": [{"name": "FLB_LOG_LEVEL", "value": "debug"}, {"name": "SERVICE_CONTAINER_NAME", "value": "ConnectUtility"}, {"name": "DD_ENV", "value": "production"}, {"name": "DD_SERVICE", "value": "connect-utility-service"}, {"name": "LOG_GROUP_NAME", "value": "/logs/connect-utility-task"}, {"name": "DD_API_KEY", "value": "********************************"}, {"name": "REGION", "value": "sa-east-1"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/connect-utility-task-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}]