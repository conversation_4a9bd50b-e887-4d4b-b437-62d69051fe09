[SERVICE]
    Parsers_File /parsers.conf
    Flush 1
    Grace 30

[FILTER]
    Name parser
    Match ${SERVICE_CONTAINER_NAME}*
    Key_Name log
    Parser json
    Reserve_Data True

[FILTER]
    name                  multiline
    match                 ${SERVICE_CONTAINER_NAME}*
    multiline.key_content log
    multiline.parser      java

[OUTPUT]
    Name                cloudwatch
    Match               ${SERVICE_CONTAINER_NAME}*
    region              ${REGION}
    log_group_name      ${LOG_GROUP_NAME}
    log_stream_prefix   ecs/

[OUTPUT]
    Name              datadog
    Match             *
    Host              http-intake.logs.datadoghq.com
    TLS               on
    compress          gzip
    apikey            ${DD_API_KEY}
    dd_service        ${DD_SERVICE}
    dd_source         logback
    dd_message_key    log
    dd_tags           env:${DD_ENV}
    provider          ecs