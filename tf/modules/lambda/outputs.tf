output "aws_lambda_function_invoke_arn" {
  value = aws_lambda_function.lambda.invoke_arn
}

output "aws_lambda_function_name" {
  value = aws_lambda_function.lambda.function_name
}

output "aws_lambda_function_arn" {
  value = aws_lambda_function.lambda.arn
}

output "aws_lambda_bucket_arn" {
  description = "ARN of the first lambda bucket (for backward compatibility)"
  value       = length(aws_s3_bucket.lambda-bucket) > 0 ? values(aws_s3_bucket.lambda-bucket)[0].arn : null
}

output "aws_lambda_bucket_arns" {
  description = "ARNs of all lambda buckets"
  value       = { for k, v in aws_s3_bucket.lambda-bucket : k => v.arn }
}

output "aws_lambda_bucket_names" {
  description = "Names of all lambda buckets"
  value       = { for k, v in aws_s3_bucket.lambda-bucket : k => v.bucket }
}