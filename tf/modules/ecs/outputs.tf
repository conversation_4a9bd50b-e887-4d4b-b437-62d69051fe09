output "alb_hostname" {
  value = aws_alb.main.dns_name
}

output "alb_zone_id" {
  value = aws_alb.main.zone_id
}

output "cluster" {
  value = aws_ecs_cluster.main
}

output "bill_payment_task_role" {
  value = module.task_role.task_role
}

output "bill_payment_task_role_arn" {
  value = module.task_role.arn
}

output "ecs_tasks_security_group_id" {
  value = try(aws_security_group.ecs_tasks.id, null)
}

output "alb_security_group_id" {
  value = try(aws_security_group.lb.id, null)
}