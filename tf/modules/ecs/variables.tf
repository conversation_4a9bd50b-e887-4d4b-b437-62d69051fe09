variable "ecr_repository_name" {
  description = "ECR repository to store our Docker images"
}

variable "ecr_repository_name_2" {
  description = "ECR repository to store our Docker images"
}

variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_private_subnet_id" {

}

variable "aws_public_subnet_id" {

}

variable "load_balance_name" {
  description = "ALB name"
}

variable "alb_target_group_name" {
  description = "ALB target group name"
}

variable "alb_target_group_name_2" {
  description = "ALB target group name"
}

variable "service_name" {
  description = "ECS service name"
}

variable "service_name_2" {
  description = "ECS service name"
}

variable "task_name" {
  description = "ECS task name"
}

variable "task_name_2" {
  description = "ECS task name"
}

variable "cluster_name" {
  description = "ECS cluster name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "ecs_task_execution_role_name" {
  description = "ECS task execution role name"
  default     = "myEcsTaskExecutionRole"
}

variable "az_count" {
  description = "Number of AZs to cover in a given region"
  default     = "2"
}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
  default     = 8080
}

variable "app_count" {
  description = "Number of docker containers to run"
  default     = 2
}

variable "app_count_2" {
  description = "Number of docker containers to run"
  default     = 1
}

variable "health_check_path" {
  default = "/"
}

variable "app_version" {
  description = "aplication version/container id"
}

variable "app_version_2" {
  description = "aplication version/container id"
}

variable "fargate_cpu" {
  description = "Fargate instance CPU units to provision (1 vCPU = 1024 CPU units)"
  default     = "256"
}

variable "fargate_memory" {
  description = "Fargate instance memory to provision (in MiB)"
  default     = "512"
}

variable "task_definition" {
  description = "path to task definition json"
}

variable "task_definition_2" {
  description = "path to task definition json"
}

variable "certificate_arn" {
  description = "arn of the certificate to be used on the ALB"
}

variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_task_role_name" {
  description = "ECS task role name"
}

variable "ecs_lambda_policy_resource" {
  description = "ECS policy resources for lambda integration"
  default     = []
}

variable "lambda_access_enabled" {
  default = false
}

variable "secrets_enabled" {
  default = false
}

variable "secrets_arns" {
  default = []
  type    = list(string)
}

/*
  Usage: "ENVIRONMENT_VARIABLE_NAME" = "SECRET_VALUE"
  {
    "INTEGRATIONS_CLEARSALE_HOST" = "${aws_secretsmanager_secret.via1_clearsale-credentials.arn}:HOST::"
  }

  Check secrets_mapping.tf for more samples
*/
variable "secrets_map" {
  type = map(string)
  default = {}
}

variable "ecs_sqs_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sqs_access_enabled" {
  default = false
}

variable "ecs_sns_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sns_access_enabled" {
  default = false
}

variable "s3_bucket_arns" {
  type    = list(string)
  default = []
}
variable "s3_read_objects" {
  default = false
}

variable "textract_enabled" {
  default = false
}

variable "send_email" {
  description = "Permission to send emails"
  default     = false
}

variable "access_logs_enabled" {
  default = false
  type    = bool
}

variable "access_logs_bucket" {
  default = ""
  type    = string
}

variable "user_pool_arn" {

}

variable "alternative_certificate_arn_enabled" {
  default = false
}

variable "alternative_certificate_arn" {
  default = ""
  type    = string
}