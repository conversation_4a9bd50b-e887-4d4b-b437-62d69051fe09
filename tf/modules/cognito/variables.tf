variable "user_pool_domain_name" {

}

variable "user_pool_domain_certificate_arn" {

}

variable "user_pool_domain_zone_id" {

}

variable "user_pool_name" {

}

variable "provider_google_app_id" {

}

variable "provider_google_app_secret" {
  sensitive = true
}

variable "notification_email_sender_arn" {
  type = string
}

variable "provider_apple_client_id" {
  type = string
}

variable "provider_apple_team_id" {
  type = string
}

variable "provider_apple_key_id" {
  type = string
}

variable "provider_apple_private_key" {
  type = string
  sensitive = true
}

variable "access_token_validity" {
  type = number
}

variable "id_token_validity" {
  type = number
}

variable "callback_urls" {
  type = list(string)
}

variable "logout_urls" {
  type = list(string)
}

variable "prefix"{
  type = string
}

variable "environment" {
  type = string
}

variable "datadog_key_arn"{
  type = string
}

variable "lambda_config_enabled" {
  description = "Habilita ou desabilita o lambda_config no User Pool"
  type        = bool
}