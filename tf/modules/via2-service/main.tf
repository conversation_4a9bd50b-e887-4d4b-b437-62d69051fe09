locals {
    incoming_messages_sqs_name = "via2_inbound_message_queue"
}

module "via2_service_image_version" {
  source         = "../../modules/image_version"
  container_name = "Via2Service"
  service_name   = "via2-service"
  cluster_name   = "via2-service-cluster"
}

resource "aws_dynamodb_table" "via2_history" {
  name         = "Friday-Via2"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "RangeKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2RangeKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PartitionKey"
    range_key       = "GSIndex2RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

data "aws_iam_policy_document" "incoming_messages_queue_policy" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:${local.incoming_messages_sqs_name}"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test     = "ArnEquals"
      values   = [var.incoming_messages_sns_topic_arn]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

module "incoming_messages_sqs" {
  source                    = "terraform-aws-modules/sqs/aws"
  version                   = "~> 2.0"
  name                      = local.incoming_messages_sqs_name
  policy                     = data.aws_iam_policy_document.incoming_messages_queue_policy.json
  message_retention_seconds = 1209600
  tags = {
    Environment = var.environment
  }
}

resource "aws_sns_topic_subscription" "incoming_emails" {
  topic_arn            = var.incoming_messages_sns_topic_arn
  protocol             = "sqs"
  endpoint             = module.incoming_messages_sqs.this_sqs_queue_arn
  filter_policy        = jsonencode({ "clientId" = tolist(["VIA2"]) })
  raw_message_delivery = true
}

resource "aws_ecs_cluster" "via2_service_cluster" {
  name = "via2-service-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

module "friday_via2_service_task" {
  source = "../../modules/fargate_task"

  prefix                = "via2"
  ecr_repository_name   = "via2-service"
  fargate_cpu           = 512
  fargate_memory        = 1024
  task_definition       = var.task_definition
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.via2_history.arn,
    "${aws_dynamodb_table.via2_history.arn}/*",
    var.shedlock_table_arn,
    "${var.shedlock_table_arn}/*",
  ]
  s3_read_objects           = false
  s3_bucket_arns            = []
  sqs_access_enabled        = true
  ecs_sqs_policy_resource   = var.ecs_sqs_list_policy_resource
  sns_access_enabled        = false
  ecs_sns_policy_resource   = []
  secrets_enabled           = true
  secrets_arns              = var.secrets_arns
  user_pool_arn_enabled     = false
  app_version               = module.via2_service_image_version.image_tag
  kms_enabled               = false
  kms_key_arns              = []
  fluent_bit_repository_url = var.fluent_bit_repository_url
  aws_region                = var.aws_region
}


module "via2_service_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = aws_ecs_cluster.via2_service_cluster
  aws_private_subnet_id   = var.private_subnets
  aws_public_subnet_id    = var.public_subnets
  aws_vpc_id              = var.vpc_id
  load_balance_enabled    = true
  prefix                  = "via2"
  container_name          = "Via2Service"
  app_port                = 8443
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.friday_via2_service_task
  certificate_arn_enabled = true
  certificate_arn         = var.certificate_arn
}