variable "environment" {
  description = "The environment"
}

variable "aws_region" {
  description = "The AWS Region"
}

variable "fluent_bit_repository_url" {
  description = "FluentBit repository url"
}

variable "task_definition" {
  description = "Task Definition file"
}

variable "vpc_id" {
  description = "vpc_id"
}

variable "private_subnets" {
  description = "private_subnets"
}

variable "public_subnets" {
  description = "public_subnets"
}

variable "certificate_arn" {
  description = "certificate_arn"
}

variable "ecs_sqs_list_policy_resource" {
  description = "ecs_sqs_list_policy_resource"
}

variable "secrets_arns" {
  description = "secrets_arns"
}

variable "incoming_messages_sns_topic_arn" {
    description = "incoming_messages_sns_topic_arn"
}

variable "account_id" {
    description = "account_id"
}

variable "shedlock_table_arn" {
    description = "shedlock_table_arn"
}