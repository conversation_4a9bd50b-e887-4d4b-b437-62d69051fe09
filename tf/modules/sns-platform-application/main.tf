module "delivery_failure_topic" {
  source = "terraform-aws-modules/sns/aws"
  name   = var.delivery_failure_topic_name
}

resource "aws_sns_topic_subscription" "event_delivery_failure" {
  topic_arn            = module.delivery_failure_topic.topic_arn
  protocol             = "sqs"
  endpoint             = module.delivery_failure_queue.this_sqs_queue_arn
  raw_message_delivery = true
}

data "aws_iam_policy_document" "delivery_failure" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = [
      var.delivery_failure_queue_arn
    ]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    condition {
      test   = "ArnEquals"
      values = [
        module.delivery_failure_topic.topic_arn
      ]
      variable = "aws:SourceArn"
    }
    effect = "Allow"
  }
}

module "delivery_failure_queue" {
  policy  = data.aws_iam_policy_document.delivery_failure.json
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = var.delivery_failure_queue_name
}

data "aws_iam_policy_document" "platform_feedback_policy" {
  statement {
    resources = ["*"]
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:PutMetricFilter",
      "logs:PutRetentionPolicy"
    ]
  }
}

resource "aws_iam_policy" "platform_feedback_role_policy" {
  policy = data.aws_iam_policy_document.platform_feedback_policy.json
}

resource "aws_iam_role_policy_attachment" "aws_secrets" {
  role       = aws_iam_role.platform_feedback_role.name
  policy_arn = aws_iam_policy.platform_feedback_role_policy.arn
}

data "aws_iam_policy_document" "assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "platform_feedback_role" {
  description        = "Logar eventos de notificação via push notification do SNS"
  name               = "SnsPlatformApplicationFeedBackRole"
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

resource "aws_sns_platform_application" "push_notification" {
  failure_feedback_role_arn        = aws_iam_role.platform_feedback_role.arn
  success_feedback_role_arn        = aws_iam_role.platform_feedback_role.arn
  name                             = var.sns_platform_application
  platform                         = "GCM"
  platform_credential              = data.aws_secretsmanager_secret_version.firebase_push_notification_secret.secret_string
  event_delivery_failure_topic_arn = module.delivery_failure_topic.topic_arn
  success_feedback_sample_rate     = "100"
}

resource "aws_secretsmanager_secret" "firebase_push_notification" {
  name = "bill-payment-api/firebase_push_notification_credential"
}

data "aws_secretsmanager_secret_version" "firebase_push_notification_secret" {
  secret_id = aws_secretsmanager_secret.firebase_push_notification.id
}
