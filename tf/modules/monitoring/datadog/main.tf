# To see all options, visit -> https://registry.terraform.io/providers/DataDog/datadog/latest/docs/resources/monitor
resource datadog_monitor default {
  name = "${var.service_name} ${var.alert_name}"
  type = "metric alert"

  query = var.alert_query

  priority = var.alert_priority

  message = <<EOT
**========= |Descrição| ========**

${var.alert_message}

**========= |Runbook| =========**

<Coloque aqui o link do seu runbook>

**======== |Notificação| ========**

{{#is_alert}}@${var.notification_channel}{{/is_alert}}
EOT

  escalation_message = <<EOT
**========= |Descrição| ========**

${var.alert_message}

**========= |Runbook| =========**

<Coloque aqui o link do seu runbook>

**======== |Notificação| ========**

{{#is_alert}}@${var.notification_channel}{{/is_alert}}
EOT


  notify_no_data    = false
  renotify_interval = 60

  notify_audit = false
  timeout_h    = 24
  include_tags = true

  tags = concat(["service:${var.service_name}", "env:${var.environment}"], var.additional_tags)
}