variable "service_name" {}
variable "environment" {}

variable "alert_name" {}
variable "alert_query" {}
variable "alert_message" {}

variable "alert_priority" {
  type        = number
  description = <<EOT
    ### Severity ###
    # 1 = P1 = Disaster
    # 2 = P2 = Business
    # 3 = P3 = High
    # 4 = P4 = Warning
    # 5 = P5 = Information
  EOT
  validation {
    condition     = contains([1, 2, 3, 4, 5], var.alert_priority)
    error_message = "alert priority must be within range 1-5."
  }
}

variable "additional_tags" {
  type    = list(string)
  default = []
}

variable "notification_channel" {
  type    = string
  default = "slack-teste-alarmes"
}