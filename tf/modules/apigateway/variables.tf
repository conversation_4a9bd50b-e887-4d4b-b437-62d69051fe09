variable "api_gateway_name" {
  description = "Name for the api gateway api"
}

variable "api_gateway_description" {
  description = "Description of the api gateway api"
}

variable "api_gateway_stage_name" {
  description = "Name for the api gateway stage"
}

variable "api_gateway_stage_description" {
  description = "Description of the api gateway stage "
}

variable "aws_lambda_function_invoke_arn" {
  description = "Arn of the lambda function to be invoked by this api gateway"
}

variable "xray_tracing_enabled" {
  description = "Should X-Ray be enabled for this api gateway"
  default     = false
}
