resource "aws_api_gateway_rest_api" "default" {
  name = var.api_gateway_name
}

resource "aws_api_gateway_resource" "resource" {
  rest_api_id = aws_api_gateway_rest_api.default.id
  parent_id   = aws_api_gateway_rest_api.default.root_resource_id
  path_part   = "{proxy+}"
}
resource "aws_api_gateway_method" "method" {
  rest_api_id   = aws_api_gateway_rest_api.default.id
  resource_id   = aws_api_gateway_resource.resource.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}
resource "aws_api_gateway_integration" "integration" {
  rest_api_id             = aws_api_gateway_rest_api.default.id
  resource_id             = aws_api_gateway_resource.resource.id
  http_method             = aws_api_gateway_method.method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = var.aws_lambda_function_invoke_arn

  request_parameters = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
}

resource "aws_api_gateway_deployment" "default" {
  rest_api_id = aws_api_gateway_rest_api.default.id
  description = var.api_gateway_description
  depends_on  = [aws_api_gateway_method.method, aws_api_gateway_integration.integration]
}

resource "aws_api_gateway_stage" "default" {
  stage_name           = var.api_gateway_stage_name
  description          = var.api_gateway_stage_description
  rest_api_id          = aws_api_gateway_rest_api.default.id
  deployment_id        = aws_api_gateway_deployment.default.id
  xray_tracing_enabled = var.xray_tracing_enabled
}