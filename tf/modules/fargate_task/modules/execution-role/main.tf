# ECS task execution role data
data "aws_iam_policy_document" "ecs_task_execution_role" {
  version = "2012-10-17"
  statement {
    sid = ""
    effect = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

# ECS task execution role
resource "aws_iam_role" "ecs_task_execution_role" {
  name               = var.ecs_task_execution_role_name
  assume_role_policy = data.aws_iam_policy_document.ecs_task_execution_role.json
}

# ECS task execution role policy attachment
resource "aws_iam_role_policy_attachment" "ecs_task_execution_role" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

data "aws_iam_policy_document" "log_publishing_policy_document" {
  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams",
      "logs:PutLogEventsBatch",
      "logs:DescribeLogStreams"
    ]
    resources = [
      "arn:aws:logs:${var.aws_region}:*:log-group:/ecs/${var.task_name}:*",
      "arn:aws:logs:${var.aws_region}:*:log-group:/logs/${var.task_name}:*"
    ]
  }
}

resource "aws_iam_policy" "log_publishing_policy" {
  name        = "${var.task_name}-log-pub"
  path        = "/"
  description = "Allow publishing to cloudwatch"

  policy = data.aws_iam_policy_document.log_publishing_policy_document.json
}

resource "aws_iam_role_policy_attachment" "ecs_log_publishing_role" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.log_publishing_policy.arn
}

data "aws_iam_policy_document" "aws_secrets" {
  statement {
    sid       = "GetSecretValuePolicy"
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = var.secrets_arns
  }
}

resource "aws_iam_policy" "aws_secrets" {
  count  = var.secrets_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.aws_secrets.json
}

resource "aws_iam_role_policy_attachment" "aws_secrets" {
  count  = var.secrets_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.aws_secrets[0].arn
}
