variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_task_role_name" {
  description = "ECS task role name"
}

variable "ecs_lambda_policy_resource" {
  description = "ECS policy resources for lambda integration"
  default = []
}

variable "lambda_access_enabled" {
  default = false
}

variable "task_name" {
  description = "ECS task name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "ecs_sqs_policy_resource" {
  default = []
}

variable "sqs_access_enabled" {
  default = false
}

variable "s3_bucket_arns" {
  type = list(string)
  default = ["*"]
}
variable "s3_read_objects" {
  default = false
}
variable "textract_enabled" {
  default = false
}
variable "send_email" {
  description = "Permission to send emails"
  default     = false
}

variable "ecs_sns_policy_resource" {
  default = ["arn:aws:sns:*:*:*"]
}

variable "sns_access_enabled" {
  default = false
}

variable "user_pool_arn_enabled" {
  default = true
}

variable "user_pool_arn" {
  default = ""
}

variable "kms_enabled" {
  default = false
}

variable "kms_key_arns" {
  default = []
}

variable "transcribe_audio_enabled" {

}