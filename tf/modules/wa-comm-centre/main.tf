module "wa_comm_centre_image_version" {
  source         = "../../modules/image_version"
  container_name = "WACommCentre"
  service_name   = "wa-comm-centre-service"
  cluster_name   = "wa-comm-centre-cluster"
}

resource "aws_dynamodb_table" "wa_comm_centre_table" {
  name         = "Friday-WACommCentre"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "RangeKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2RangeKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PartitionKey"
    range_key       = "GSIndex2RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_secretsmanager_secret" "whatsapp_key" {
  name = "friday/whatsapp_key"
}

resource "aws_ecs_cluster" "wa_comm_centre_cluster" {
  name ="wa-comm-centre-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

module "incoming_wa_messages" {
  source = "terraform-aws-modules/sns/aws"
  name   = "incoming_wa_messages"
}

module "wa_comm_centre_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "wa_comm_centre_dlq"

  tags = {
    Environment = var.environment
  }
}

module "wa_comm_centre_task" {
  source = "../../modules/fargate_task"

  prefix                     = "wa-comm-centre"
  ecr_repository_name        = "wa-comm-centre"
  fargate_cpu                = 1024
  fargate_memory             = 2048
  task_definition            = var.task_definition
  dynamo_access_enabled      = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.wa_comm_centre_table.arn,
    "${aws_dynamodb_table.wa_comm_centre_table.arn}/*",
  ]
  s3_read_objects           = true
  s3_bucket_arns            = [aws_s3_bucket.wa_comm_centre_bucket.arn]
  sqs_access_enabled        = true
  ecs_sqs_policy_resource   = var.ecs_sqs_policy_resource
  sns_access_enabled        = true
  ecs_sns_policy_resource   = [module.incoming_wa_messages.topic_arn]
  secrets_enabled           = true
  secrets_arns              = [aws_secretsmanager_secret.whatsapp_key.arn]
  user_pool_arn_enabled     = false
  app_version               = module.wa_comm_centre_image_version.image_tag
  kms_enabled               = false
  kms_key_arns              = []
  fluent_bit_repository_url = var.fluent_bit_repository_url
  aws_region                = var.aws_region
}

module "wa_comm_centre_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = aws_ecs_cluster.wa_comm_centre_cluster
  aws_private_subnet_id   = var.private_subnets
  aws_public_subnet_id    = var.public_subnets
  aws_vpc_id              = var.vpc_id
  load_balance_enabled    = true
  prefix                  = "wa-comm-centre"
  container_name          = "WACommCentre"
  app_port                = 8443
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.wa_comm_centre_task
  certificate_arn_enabled = true
  certificate_arn         = var.certificate_arn
  mtls_enabled            = true
  mtls_certificate_bucket = var.mtls_certificate_bucket
  mtls_certificate_key    = var.mtls_certificate_key
  mtls_paths              = ["/webhook/*"]
}

resource "aws_s3_bucket" "wa_comm_centre_bucket" {
  bucket = var.wa_comm_centre_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_versioning" "wa_comm_centre_bucket" {
  bucket = aws_s3_bucket.wa_comm_centre_bucket.id
  versioning_configuration {
    status     = "Disabled"
    mfa_delete = "Disabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "wa_comm_centre_bucket" {
  bucket = aws_s3_bucket.wa_comm_centre_bucket.id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}