locals {
  efs_subnet_id = var.aws_private_subnet_id[0]
}

resource "aws_ecr_repository" "default" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name
  tags                 = var.tags
}

module "execution_role" {
  source = "./modules/execution-role"

  ecs_task_execution_role_name = var.ecs_task_execution_role_name
  secrets_enabled              = var.secrets_enabled
  secrets_arns                 = var.secrets_arns
  task_name                    = var.task_name
  aws_region                   = var.aws_region
}

module "task_role" {
  source = "./modules/task-role"

  task_name                  = var.task_name
  aws_region                 = var.aws_region
  ecs_dynamo_policy_resource = var.ecs_dynamo_policy_resource
  ecs_task_role_name         = var.ecs_task_role_name
  dynamo_access_enabled      = var.dynamo_access_enabled
  s3_bucket_arns             = var.s3_bucket_arns
  s3_read_objects            = var.s3_read_objects
  sqs_access_enabled         = var.sqs_access_enabled
  ecs_sqs_policy_resource    = var.ecs_sqs_policy_resource
}

resource "aws_cloudwatch_log_group" "default" {
  name = "/ecs/${var.task_name}"
  tags = var.tags
}

# ALB Security group
# This is the group you need to edit if you want to restrict access to your application
resource "aws_security_group" "lb" {
  name        = "liveness-alb"
  description = "controls access to the ALB"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol  = "tcp"
    from_port = 8080
    to_port   = 8080
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol  = "tcp"
    from_port = 443
    to_port   = 443
    cidr_blocks = ["0.0.0.0/0"] # add a CIDR block here
  }

  egress {
    from_port = 0
    to_port   = 0
    protocol  = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = var.tags
}

# Traffic to the ECS Cluster should only come from the ALB
resource "aws_security_group" "ecs_tasks" {
  name        = "liveness-tasks"
  description = "allow inbound access from the ALB only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol  = "tcp"
    from_port = var.app_port
    to_port   = var.app_port
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    protocol  = "-1"
    from_port = 0
    to_port   = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = var.tags
}

resource "aws_alb" "main" {
  name         = var.load_balance_name
  subnets      = var.aws_public_subnet_id
  security_groups = [aws_security_group.lb.id]
  idle_timeout = 180
  access_logs {
    bucket  = var.access_logs_bucket
    enabled = var.access_logs_enabled
  }
  tags = var.tags
}

resource "aws_alb_target_group" "app" {
  name                 = var.alb_target_group_name
  port                 = var.app_port
  protocol             = "HTTPS"
  vpc_id               = var.aws_vpc_id
  target_type          = var.use_fargate ? "ip" : "instance"
  deregistration_delay = 60

  health_check {
    healthy_threshold   = "3"
    unhealthy_threshold = "10"
    interval            = "120"
    protocol            = "HTTPS"
    matcher             = "200-499"
    timeout             = "50"
    path                = var.health_check_path
  }
  tags = var.tags
}

# Redirect all traffic from the ALB to the target group
resource "aws_alb_listener" "front_end" {
  load_balancer_arn = aws_alb.main.id
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  default_action {
    target_group_arn = aws_alb_target_group.app.id
    type             = "forward"
  }
  tags = var.tags
}

resource "aws_ecs_cluster" "main" {
  name = var.cluster_name
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
  tags = var.tags
}


module "liveness_image_version" {
  source         = "../image_version"
  container_name = var.container_name
  cluster_name   = var.cluster_name
  service_name   = var.service_name
}

module "liveness_efs" {
  count                       = var.environment == "production" ? 1 : 0
  source                      = "./modules/liveness-efs"
  tags                        = var.tags
  aws_vpc_id                  = var.aws_vpc_id
  efs_subnet_id               = local.efs_subnet_id
  aws_security_group_tasks_id = aws_security_group.ecs_tasks.id
}

resource "aws_ecs_task_definition" "app" {
  family             = var.task_name
  execution_role_arn = module.execution_role.arn
  task_role_arn      = module.task_role.arn
  network_mode       = var.use_fargate ? "awsvpc" : "bridge"
  requires_compatibilities = var.use_fargate ? ["FARGATE"] : ["EC2"]
  cpu                = var.fargate_cpu
  memory             = var.fargate_memory
  dynamic "volume" {
    for_each = var.environment =="production" ? [0] : []
    content {
      name = "liveness-gateway-efs-volume"
      efs_volume_configuration {
        file_system_id = module.liveness_efs[0].volume_id
      }
    }
  }

  dynamic "volume" {
    for_each = var.environment =="production" ? [0] : []
    content {
      name = "docker_sock"
      host_path = "/var/run/docker.sock"
    }
  }

  dynamic "volume" {
    for_each = var.environment =="production" ? [0] : []
    content {
      name = "proc"
      host_path = "/proc/"
    }
  }

  dynamic "volume" {
    for_each = var.environment =="production" ? [0] : []
    content {
      name = "cgroup"
      host_path = "/sys/fs/cgroup/"
    }
  }

  container_definitions = templatefile(var.task_definition, {
    app_image                 = "${aws_ecr_repository.default.repository_url}:${module.liveness_image_version.image_tag}"
    app_port                  = var.app_port
    container_name            = var.container_name
    service_name              = "liveness-gateway"
    task_name                 = var.task_name
    secret_arns               = var.secrets_arns
    environment               = var.environment
    fluent_bit_repository_url = var.fluent_bit_repository_url
    usage_logs_server_url     = var.usage_logs_enabled ? module.usage_logs_server[0].url : ""
  })
  tags = var.tags
}

resource "aws_ecs_service" "main" {
  name             = var.service_name
  cluster          = aws_ecs_cluster.main.id
  task_definition  = aws_ecs_task_definition.app.arn
  desired_count    = var.app_count
  launch_type      = var.use_fargate ? "FARGATE" : "EC2"
  platform_version = var.use_fargate ? "1.4.0": null
  tags             = var.tags

  dynamic "network_configuration" {
    for_each = var.use_fargate ? [1] : []
    content {
      security_groups = [aws_security_group.ecs_tasks.id]
      subnets = [var.aws_private_subnet_id[0]]
      assign_public_ip = true
    }
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.app.id
    container_name   = var.container_name
    container_port   = var.app_port
  }

  depends_on = [aws_alb_listener.front_end, module.execution_role.role_policy_attachment_execution_role]
}

resource "aws_cloudwatch_log_group" "liveness-gateway-dd" {
  name              = "/ecs/liveness-gateway-dd"
  retention_in_days = 7
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "liveness-gateway" {
  name = "/logs/liveness-gateway-task"
  tags = var.tags
}

module usage_logs_server {
  source                       = "./modules/usage-logs-server"
  service_name                 = "usage_logs_server_service"
  container_name               = "UsageLogsServer"
  cluster                      = aws_ecs_cluster.main
  count                        = var.usage_logs_enabled ? 1 : 0
  aws_vpc_id                   = var.aws_vpc_id
  aws_private_subnet_id        = var.aws_private_subnet_id
  aws_public_subnet_id         = var.aws_public_subnet_id
  ingress_security_group       = aws_security_group.ecs_tasks.id
  task_name                    = "liveness-usage-logs-task"
  secrets_enabled              = var.secrets_enabled
  secrets_arns                 = var.usage_logs_secrets_arns
  ecs_task_role_name           = "liveness-usage-logs-task-role"
  ecs_task_execution_role_name = "liveness-usage-logs-task-execution-role"
  app_port                     = 3000
  tags                         = var.tags
  environment                  = var.environment
}

module "ec2" {
  source                = "./modules/ec2"
  count                 = var.use_fargate ? 0 : 1
  aws_vpc_id            = var.aws_vpc_id
  aws_alb_security_group_id = aws_security_group.lb.id
  aws_ecs_cluster_name  = aws_ecs_cluster.main.name
  asg_min_size          = 1
  asg_desired_capacity  = 1
  asg_max_size          = 3
  instance_type         = var.environment =="production"? "c6i.2xlarge" : "t3.medium"
  ec2_name              = "liveness-gateway-ec2"
  app_port              = var.app_port
  efs_subnet_id    = local.efs_subnet_id
  secrets_arns     = [var.secrets_arns[0]]
  tags = var.tags
}