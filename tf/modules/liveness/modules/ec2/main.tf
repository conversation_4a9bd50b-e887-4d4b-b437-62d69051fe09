# --- ECS Node Role ---

data "aws_iam_policy_document" "ecs_node_doc" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "ecs_node_role" {
  name_prefix        = "${var.ec2_name}-node-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_node_doc.json
}

resource "aws_iam_role_policy_attachment" "ecs_node_role_policy" {
  role       = aws_iam_role.ecs_node_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_instance_profile" "ecs_node" {
  name_prefix = "${var.ec2_name}-node-profile"
  path        = "/ecs/instance/"
  role        = aws_iam_role.ecs_node_role.name
}

# --- ECS Node SG ---

resource "aws_security_group" "ecs_node_sg" {
  name_prefix = "${var.ec2_name}-node-sg-"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.app_port
    to_port         = var.app_port
    security_groups = [var.aws_alb_security_group_id]
  }

  egress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# --- ECS Launch Template ---

data "aws_ssm_parameter" "ecs_node_ami" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}

resource "aws_launch_template" "ecs_ec2" {
  name_prefix            = "${var.ec2_name}-ec2-lt-"
  image_id               = data.aws_ssm_parameter.ecs_node_ami.value
  instance_type          = var.instance_type
  vpc_security_group_ids = [aws_security_group.ecs_node_sg.id]
  update_default_version = true

  iam_instance_profile { arn = aws_iam_instance_profile.ecs_node.arn }
  monitoring { enabled = true }

  user_data = base64encode(<<-EOF
      #!/bin/bash
      echo ECS_CLUSTER=${var.aws_ecs_cluster_name} >> /etc/ecs/ecs.config;
    EOF
  )
}

# --- ECS ASG ---

resource "aws_autoscaling_group" "ecs" {
  name_prefix               = "${var.ec2_name}-asg-"
  vpc_zone_identifier       = [var.efs_subnet_id]
  min_size                  = var.asg_min_size
  max_size                  = var.asg_max_size
  desired_capacity          = var.asg_desired_capacity
  health_check_grace_period = 0
  health_check_type         = "EC2"
  protect_from_scale_in     = false

  launch_template {
    id      = aws_launch_template.ecs_ec2.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = var.aws_ecs_cluster_name
    propagate_at_launch = true
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = ""
    propagate_at_launch = true
  }
}

# --- ECS Capacity Provider ---

resource "aws_ecs_capacity_provider" "main" {
  name = "${var.ec2_name}-capacity-provider"

  auto_scaling_group_provider {
    auto_scaling_group_arn         = aws_autoscaling_group.ecs.arn
    managed_termination_protection = "DISABLED"

    managed_scaling {
      maximum_scaling_step_size = 1
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 100
    }
  }
}

resource "aws_ecs_cluster_capacity_providers" "main" {
  cluster_name       = var.aws_ecs_cluster_name
  capacity_providers = [aws_ecs_capacity_provider.main.name]

  default_capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.main.name
    base              = 1
    weight            = 100
  }
}

resource "aws_autoscaling_policy" "ecs_scale_out" {
  name                   = "ecs-scale-out"
  scaling_adjustment     = -1
  adjustment_type        = "ChangeInCapacity"
  cooldown               = 300
  autoscaling_group_name = aws_autoscaling_group.ecs.name
}

resource "aws_autoscaling_policy" "ecs_scale_in" {
  name                   = "ecs-scale-in"
  scaling_adjustment     = 1
  adjustment_type        = "ChangeInCapacity"
  cooldown               = 300
  autoscaling_group_name = aws_autoscaling_group.ecs.name
}

resource "aws_cloudwatch_metric_alarm" "ecs_scale_in_cpu" {
  alarm_name          = "ecs-scale-in-by-CPU"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period             = "30"
  statistic          = "Average"
  threshold          = "100"
  alarm_description  = "This metric monitors ECS CPU utilization"
  alarm_actions      = [aws_autoscaling_policy.ecs_scale_in.arn]

  dimensions = {
    ClusterName = var.aws_ecs_cluster_name
  }
}

resource "aws_cloudwatch_metric_alarm" "ecs_scale_out_cpu" {
  alarm_name          = "ecs-scale-out-by-CPU"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period             = "30"
  statistic          = "Average"
  threshold          = "100"
  alarm_description  = "This metric monitors ECS CPU utilization"
  alarm_actions      = [aws_autoscaling_policy.ecs_scale_out.arn]

  dimensions = {
    ClusterName = var.aws_ecs_cluster_name
  }
}

resource "aws_cloudwatch_metric_alarm" "ecs_scale_in_memory" {
  alarm_name          = "ecs-scale-in-by-memory"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period             = "30"
  statistic          = "Average"
  threshold          = "100"
  alarm_description  = "This metric monitors ECS Memory utilization"
  alarm_actions      = [aws_autoscaling_policy.ecs_scale_in.arn]

  dimensions = {
    ClusterName = var.aws_ecs_cluster_name
  }
}

# --- ECS Datadog Daemon Task Definition ---
resource "aws_ecs_task_definition" "datadog_daemon" {
  family                   = "datadog-daemon"
  network_mode            = "bridge"
  requires_compatibilities = ["EC2"]

  volume {
    name      = "docker_sock"
    host_path = "/var/run/docker.sock"
  }

  volume {
    name      = "proc"
    host_path = "/proc/"
  }

  volume {
    name      = "cgroup"
    host_path = "/sys/fs/cgroup/"
  }

  container_definitions = templatefile("${path.module}/task-definitions/datadog-daemon.json", {
    secret_arns = var.secrets_arns
  })
}

resource "aws_ecs_service" "datadog_daemon" {
  name            = "datadog-daemon"
  cluster         = var.aws_ecs_cluster_name
  task_definition = aws_ecs_task_definition.datadog_daemon.arn
  desired_count   = 1
  launch_type     = "EC2"
  scheduling_strategy = "DAEMON"
}

resource "aws_cloudwatch_log_group" "datadog_daemon" {
  name              = "/ecs/datadog-daemon"
  retention_in_days = 7
  tags              = var.tags
}