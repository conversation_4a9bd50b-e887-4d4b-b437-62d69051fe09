[{"name": "datadog-agent", "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1", "cpu": 10, "memory": 256, "essential": true, "environment": [{"name": "DD_API_KEY", "value": "${secret_arns[0]}:DD_API_KEY::"}, {"name": "DD_SITE", "value": "datadoghq.com"}, {"name": "ECS_FARGATE", "value": "false"}, {"name": "DD_APM_ENABLED", "value": "true"}, {"name": "DD_APM_NON_LOCAL_TRAFFIC", "value": "true"}, {"name": "DD_APM_RECEIVER_PORT", "value": "8126"}, {"name": "DD_DOGSTATSD_NON_LOCAL_TRAFFIC", "value": "true"}], "mountPoints": [{"sourceVolume": "docker_sock", "containerPath": "/var/run/docker.sock", "readOnly": true}, {"sourceVolume": "proc", "containerPath": "/host/proc", "readOnly": true}, {"sourceVolume": "cgroup", "containerPath": "/host/sys/fs/cgroup", "readOnly": true}], "portMappings": [{"containerPort": 8125, "hostPort": 8125, "protocol": "udp"}, {"containerPort": 8126, "hostPort": 8126, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/datadog-daemon", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]