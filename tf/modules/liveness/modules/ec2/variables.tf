variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "ec2_name" {
  description = "nome do grupo de instancias do ec2"
}

variable "app_port" {
    description = "Port exposed by the docker image to redirect traffic to"
    default     = 8080
}

variable "aws_alb_security_group_id" {
    description = "ALB security group id"
}

variable "aws_ecs_cluster_name"{
    description = "ECS cluster name"
}

variable "asg_min_size"{
    description = "Minimum size of the ASG"
    default     = 1
}

variable "asg_max_size" {
    description = "Maximum size of the ASG"
    default     = 1
}

variable "asg_desired_capacity" {
    description = "current desired size of the ASG"
    default     = 1
}

variable "instance_type" {
    description = "Instance type"
}

variable "efs_subnet_id" {}

variable "secrets_arns" {
  description = "Lista de ARNs dos segredos necessários para o Datadog"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "Tags to use on project components"
  type = map
}