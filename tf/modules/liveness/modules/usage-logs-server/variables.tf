variable "ecr_repository_name" {
  description = "ECR repository to store our Docker images"
  default = "liveness-gateway-usage-logs-server"
}

variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_private_subnet_id" {
  description = "aws_private_subnet id"
}

variable "aws_public_subnet_id" {
  description = "aws_public_subnet id"
}

variable "ingress_security_group" {

}

/*variable "load_balance_name" {
  description = "ALB name"
}

variable "alb_target_group_name" {
  description = "ALB target group name"
}*/

/*variable "service_name" {
  description = "ECS service name"
}



variable "cluster_name" {
  description = "ECS cluster name"
}*/

variable "task_name" {
  description = "ECS task name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "ecs_task_execution_role_name" {
  description = "ECS task execution role name"
}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
}

variable "app_count" {
  description = "Number of docker containers to run"
  default     = 1
}

variable "secrets_enabled" {
  default = false
}

variable "secrets_arns" {
  default = []
  type    = list(string)
}

variable "access_logs_enabled" {
  default = false
  type    = bool
}

variable "access_logs_bucket" {
  default = ""
  type    = string
}

variable "tags" {
  description = "Tags to use on project components"
  type = map
}

variable "ecs_task_role_name" {
  description = "ECS task role name"
}

variable "container_name" {
  description = "main app container name"
}

variable "cluster" {
  description = "ECS cluster"
}

variable "service_name" {
  description = "ECS service name"
}

variable "environment" {
  description = "The environment"
}