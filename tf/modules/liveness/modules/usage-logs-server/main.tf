resource "aws_ecr_repository" "default" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name
  tags                 = var.tags
}

module "execution_role" {
  source = "../execution-role"

  ecs_task_execution_role_name = var.ecs_task_execution_role_name
  secrets_enabled              = var.secrets_enabled
  secrets_arns                 = var.secrets_arns
  task_name                    = var.task_name
  aws_region                   = var.aws_region
}

module "task_role" {
  source = "../task-role"

  task_name                  = var.task_name
  aws_region                 = var.aws_region
  ecs_dynamo_policy_resource = []
  ecs_task_role_name = var.ecs_task_role_name
}

resource "aws_cloudwatch_log_group" "default" {
  name = "/ecs/${var.task_name}"
  tags = var.tags
}

resource "aws_cloudwatch_log_group" "default-dd" {
  name = "/ecs/${var.task_name}-dd"
  retention_in_days = 7
  tags = var.tags
}

# This is the group you need to edit if you want to restrict access to your application
resource "aws_security_group" "lb" {
  name        = "usage-logs-lb-sg"
  description = "controls access to the usage_logs_server"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 80
    to_port     = 80
    cidr_blocks = ["0.0.0.0/0"]
    #security_groups = [var.ingress_security_group]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = var.tags
}

resource "aws_security_group" "ecs_tasks" {
  name        = "usage-logs-tasks-sg"
  description = "allow inbound access from the ALB only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.app_port
    to_port         = var.app_port
    security_groups = [aws_security_group.lb.id]
  }

  ingress {
    protocol        = "tcp"
    from_port       = 2049
    to_port         = 2049
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = var.tags
}

resource "aws_alb" "main" {
  name            = "usage-logs-alb"
  subnets         = var.aws_public_subnet_id
  security_groups = [aws_security_group.lb.id]
  internal = true

  access_logs {
    bucket  = var.access_logs_bucket
    enabled = var.access_logs_enabled
  }
  tags = var.tags
}

resource "aws_alb_target_group" "app" {
  name                 = "usage-logs-server-tg"
  port                 = var.app_port
  protocol             = "HTTP"
  vpc_id               = var.aws_vpc_id
  target_type          = "ip"
  deregistration_delay = 120

  health_check {
    healthy_threshold   = "3"
    unhealthy_threshold = "6"
    interval            = "60"
    protocol            = "HTTP"
    matcher             = "200-499"
    timeout             = "50"
    path                = "/health"
  }
  tags = var.tags
}

# Redirect all traffic from the ALB to the target group
resource "aws_alb_listener" "front_end" {
  load_balancer_arn = aws_alb.main.id
  port              = 80
  protocol          = "HTTP"

  default_action {
    target_group_arn = aws_alb_target_group.app.id
    type             = "forward"
  }
  tags = var.tags
}

module "liveness_usage_logs_server_image_version"{
  source         = "../../../image_version"
  container_name = var.container_name
  cluster_name   = var.cluster.name
  service_name   = var.service_name
}

resource "aws_efs_file_system" "usage_logs_server" {
  tags = merge(tomap({Name = "usage-logs-efs-volume"}),
    var.tags,
    )
}

resource "aws_efs_mount_target" "usage_logs_server" {
  file_system_id = aws_efs_file_system.usage_logs_server.id
  subnet_id      = var.aws_private_subnet_id[0]
  security_groups = [ aws_security_group.usage_logs_efs_volume.id ]
  # tags           = var.tags
}

resource "aws_security_group" "usage_logs_efs_volume" {
  name        = "usage_logs_efs_volume-sg"
  description = "allow inbound access from the ECS task only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = 2049
    to_port         = 2049
    cidr_blocks = ["0.0.0.0/0"]
    security_groups = [aws_security_group.ecs_tasks.id]
  }

  tags = var.tags
}

resource "aws_ecs_task_definition" "usage_logs_server" {
  family = "usage-logs-server"
  tags = var.tags
  execution_role_arn = module.execution_role.arn
  network_mode = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = 512
  memory                   = 1024
  volume {
    name = "usage-logs-efs-volume"
    efs_volume_configuration {
      file_system_id = aws_efs_file_system.usage_logs_server.id
    }
  }
  container_definitions = <<EOF
[
  {
    "name": "UsageLogsServer",
    "image": "${aws_ecr_repository.default.repository_url}:${module.liveness_usage_logs_server_image_version.image_tag}",
    "cpu": 502,
    "memory": 768,
    "environment" : [
      {
        "name": "SERVER_PORT",
        "value": "3000"
      },
      {
        "name": "USAGE_LOGS_DIRECTORY",
        "value": "/logs"
      },
      {
        "name": "DD_ENV",
        "value": "${var.environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${var.service_name}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${var.environment}",
      "com.datadoghq.tags.service": "${var.service_name}"
    },
    "secrets": [
      {
        "name": "DEVICE_KEY_IDENTIFIER",
        "valueFrom": "${var.secrets_arns[1]}:DEVICE_KEY_IDENTIFIER::"
      },
      {
        "name": "SERVER_KEY",
        "valueFrom": "${var.secrets_arns[1]}:SERVER_KEY::"
      },
      {
        "name": "PRODUCTION_KEY_TEXT",
        "valueFrom": "${var.secrets_arns[1]}:PRODUCTION_KEY_TEXT::"
      },
      {
        "name": "HTTPS_API_KEY",
        "valueFrom": "${var.secrets_arns[1]}:USAGE_LOGS_HTTPS_API_KEY::"
      }
    ],
    "portMappings": [
      {
        "containerPort": 3000,
        "hostPort": 3000
      },
      {
        "containerPort": 2049,
        "hostPort": 2049
      }
    ],
    "mountPoints": [
        {
            "sourceVolume": "usage-logs-efs-volume",
            "containerPath": "/logs"
        }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${var.task_name}",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "381563809177.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${var.secrets_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${var.task_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  }
]
EOF
}

resource "aws_ecs_service" "usage_logs_server" {
  name             = var.service_name
  cluster          = var.cluster.id
  task_definition  = aws_ecs_task_definition.usage_logs_server.arn
  desired_count    = 1
  launch_type      = "FARGATE"
  platform_version = "1.4.0"
  tags             = var.tags

  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = [var.aws_private_subnet_id[0]]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.app.id
    container_name   = var.container_name
    container_port   = var.app_port
  }

  depends_on = [aws_alb_listener.front_end, module.execution_role.role_policy_attachment_execution_role]
}