variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_task_role_name" {
  description = "ECS task role name"
}

variable "task_name" {
  description = "ECS task name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "s3_bucket_arns" {
  type    = list(string)
  default = []
}
variable "s3_read_objects" {
  default = false
}

variable "ecs_sqs_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sqs_access_enabled" {
  default = false
}