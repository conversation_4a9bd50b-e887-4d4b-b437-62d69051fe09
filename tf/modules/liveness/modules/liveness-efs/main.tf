resource "aws_efs_file_system" "liveness_gateway_efs_volume" {
  tags = merge(tomap({Name = "liveness-gateway-efs-volume"}),
    var.tags,
  )
}

resource "aws_security_group" "liveness_gateway_efs_volume" {
  name        = "liveness_gateway_efs_volume-sg"
  description = "allow inbound access from the ECS task only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = 2049
    to_port         = 2049
    cidr_blocks = ["0.0.0.0/0"]
    security_groups = [var.aws_security_group_tasks_id]
  }

  tags = var.tags
}

resource "aws_efs_mount_target" "liveness_gateway_efs_volume" {
  file_system_id = aws_efs_file_system.liveness_gateway_efs_volume.id
  subnet_id      = var.efs_subnet_id
  security_groups = [ aws_security_group.liveness_gateway_efs_volume.id ]
  # tags           = var.tags
}