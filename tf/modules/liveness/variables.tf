variable "ecr_repository_name" {
  description = "ECR repository to store our Docker images"
}

variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_private_subnet_id" {

}

variable "aws_public_subnet_id" {

}

variable "load_balance_name" {
  description = "ALB name"
}

variable "alb_target_group_name" {
  description = "ALB target group name"
}

variable "service_name" {
  description = "ECS service name"
}

variable "task_name" {
  description = "ECS task name"
}

variable "cluster_name" {
  description = "ECS cluster name"
}

variable "aws_region" {
  description = "The AWS region things are created in"
  default     = "us-east-1"
}

variable "ecs_task_execution_role_name" {
  description = "ECS task execution role name"
  default     = "livenessTaskExecutionRole"
}

variable "az_count" {
  description = "Number of AZs to cover in a given region"
  default     = "2"
}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
  default     = 8080
}

variable "app_count" {
  description = "Number of docker containers to run"
  default     = 2
}

variable "health_check_path" {
  default = "/"
}

variable "fargate_cpu" {
  description = "Fargate instance CPU units to provision (1 vCPU = 1024 CPU units)"
  default     = "256"
}

variable "fargate_memory" {
  description = "Fargate instance memory to provision (in MiB)"
  default     = "512"
}

variable "task_definition" {
  description = "path to task definition json"
}

variable "certificate_arn" {
  description = "arn of the certificate to be used on the ALB"
}

variable "dynamo_access_enabled" {
  default     = false
  description = "Dynamo access"
}

variable "ecs_dynamo_policy_resource" {
  description = " ECS dynamo policy resources"
}

variable "ecs_task_role_name" {
  description = "ECS task role name"
}

variable "usage_logs_enabled" {
  type    = bool
  default = false
}

variable "secrets_enabled" {
  default = false
}

variable "secrets_arns" {
  default = []
  type    = list(string)
}

variable "usage_logs_secrets_arns" {
  default = []
  type    = list(string)
}

variable "s3_bucket_arns" {
  type    = list(string)
  default = []
}

variable "s3_read_objects" {
  default = false
}

variable "access_logs_enabled" {
  default = false
  type    = bool
}

variable "access_logs_bucket" {
  default = ""
  type    = string
}

variable "alternative_certificate_arn_enabled" {
  default = false
  type    = bool
}

variable "alternative_certificate_arn" {
  default = ""
  type    = string
}

variable "environment" {
  description = "The environment"
}

variable "fluent_bit_repository_url" {
  description = "The fluent bit repository URL"
}

variable "container_name" {
  description = "main app container name"
}

variable "tags" {
  description = "Tags to use on project components"
  type = map
}

variable "ecs_sqs_policy_resource" {
  description = "ECS policy resources for sqs integration"
  default     = []
}

variable "sqs_access_enabled" {
  default = false
}
variable "use_fargate" {
  default = true
}