variable "aws_vpc_id" {
  description = "aws_vpc id"
}

variable "aws_private_subnet_id" {

}

variable "aws_public_subnet_id" {

}

variable "load_balance_enabled" {
  description = "ALB enabled"
  default     = false
}

variable "target_group_protocol" {
  description = "Protocol to use on the target group"
  default     = "HTTPS"

  validation {
    condition = contains(["HTTP", "HTTPS"], var.target_group_protocol)
    error_message = "Invalid protocol. Must be HTTP or HTTPS"
  }
}

variable "prefix" {
  description = "Prefix to use on ALB, target group and service names"
}

variable "task_definition" {
  description = "ECS task definition"
}

variable "aws_ecs_cluster" {
  description = "ECS cluster"
}

variable "app_port" {
  description = "Port exposed by the docker image to redirect traffic to"
  default     = 8080
}

variable "container_name" {
  description = "Name of the container to target external requisitions"
}

variable "app_count" {
  description = "Number of docker containers to run"
  default     = 2
}

variable "health_check_path" {
  default = "/"
}

variable "access_logs_enabled" {
  default = false
  type    = bool
}

variable "access_logs_bucket" {
  default = ""
  type    = string
}

variable "certificate_arn_enabled" {
  default = true
}

variable "certificate_arn" {
  description = "arn of the certificate to be used on the ALB"
  default     = ""
}

variable "alternative_certificate_arn_enabled" {
  default = false
}

variable "alternative_certificate_arn" {
  default = ""
  type    = string
}

variable "alb_idle_timeout" {
  default     = 60
  type        = number
  description = "Idle timeout for the ALB"
}

variable "mtls_enabled" {
  description = "Whether to enable mTLS for the ALB"
  type        = bool
  default     = false
}

variable "mtls_certificate_bucket" {
  description = "S3 bucket containing the mTLS certificate"
  type        = string
  default     = ""
}

variable "mtls_certificate_key" {
  description = "S3 key of the mTLS certificate"
  type        = string
  default     = ""
}

variable "mtls_paths" {
  description = "List of paths that require mTLS authentication"
  type        = list(string)
  default     = []
}