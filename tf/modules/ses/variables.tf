variable "ses_receiver_rule_name" {
  description = "The name of the recipiment rule of SES"
}

variable "scan_enabled" {
  description = "Scan for viruses, etc"
}

variable "ses_bucket_name" {
  description = "Bucket name that store the emails"
}

variable "ses_incoming_emails_bucket_name" {
  type    = string
  default = null
}

variable "ses_incoming_emails_bucket_replication_enabled" {
  type    = bool
  default = false
}

variable "ses_unprocessed_emails_bucket_name" {
  description = "Bucket name of emails that failed to process on email-receiver"
}

variable "ses_unprocessed_emails_bucket_replication_enabled" {
  type    = bool
  default = false
}

variable "region" {
  description = "Aws region"
}

variable "rendering_errors_to_s3_bucket_enabled" {
  type    = bool
  default = false
}


variable "rendering_bucket_name" {
  type    = string
  default = ""
}

variable "sns_receiver_emails_arn" {
  type    = string
  default = null
}

variable "sns_failure_rendering_arn" {
  type    = string
  default = null
}

variable "email_domain" {
  type    = list(string)
  default = []
}

variable "quarantine_emails_bucket_name" {
  type    = string
  default = null
}

variable "notification_email_sender" {
  type    = string
}

variable "ses_incoming_emails_bucket_replication_rule_id" {
  type    = string
  default = null
}

variable "ses_incoming_emails_bucket_replication_destination" {
  type    = string
  default = null
}

variable "ses_incoming_emails_bucket_replication_role" {
  type    = string
  default = null
}

variable "ses_unprocessed_emails_bucket_replication_rule_id" {
  type    = string
  default = null
}

variable "ses_unprocessed_emails_bucket_replication_destination" {
  type    = string
  default = null
}

variable "ses_unprocessed_emails_bucket_replication_role" {
  type    = string
  default = null
}
