resource "aws_s3_bucket" "default" {
  count = var.create_bucket ? 1 : 0
  bucket   = var.bucket_name
  acl      = "private"

  versioning {
    enabled = true
  }

  dynamic "replication_configuration" {
    for_each = toset(var.replication_enabled ? ["fake"] : [])
    content {
      //TODO trazer a role para o terraform
      role = var.replication_role
      rules {
        id = var.replication_rule_id
        priority = 0
        status = "Enabled"
        destination {
          //TODO variavel?
          account_id = "************"
          //TODO trazer o bucket para o terraform
          bucket = var.replication_bucket_destination
          access_control_translation {
            owner = "Destination"
          }
        }
      }
    }
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  lifecycle_rule {
    prefix  = ""
    enabled = false

    transition {
      days          = 1825
      storage_class = "GLACIER"
    }
    noncurrent_version_transition {
      days          = 1825
      storage_class = "GLACIER"
    }
  }
}

resource "aws_s3_bucket_policy" "default" {
  count = var.has_policy && var.create_bucket ? 1 : 0
  bucket = var.bucket_name
  policy = var.policy_json
  depends_on = [aws_s3_bucket.default]
}