variable "bucket_name" {
  type = string
}

variable "region" {
  type = string
}

variable "has_policy" {
  type = bool
  default = false
}

variable "policy_json" {
  type = string
  default = ""
}

variable "create_bucket" {
  type = bool
  default = true
}

variable "replication_enabled" {
  type = bool
  default = false
}

variable "replication_rule_id" {
  type = string
  default = ""
}

variable "replication_bucket_destination" {
  type = string
  default = ""
}

variable "replication_role" {
  type = string
  default = ""
}
