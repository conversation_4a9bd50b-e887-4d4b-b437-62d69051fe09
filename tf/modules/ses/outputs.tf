output "email_receiver_s3_bucket_id" {
  description = "The ID of the S3 bucket"
  value       = module.received_emails.id
}

output "email_receiver_s3_bucket_arn" {
  description = "The ARN of the S3 bucket"
  value       = module.received_emails.arn
}

output "unprocessed_emails_bucket_id" {
  value = module.unprocessed_emails.id
}

output "unprocessed_emails_bucket_arn" {
  value = module.unprocessed_emails.arn
}

output "quarantine_emails_bucket_arn" {
  value = module.quarantine_emails.arn
}

output "quarantine_emails_bucket_id" {
  value = module.quarantine_emails.id
}

output "incoming_emails_bucket_id" {
  value = module.incoming_emails.id
}

output "incoming_emails_bucket_arn" {
  value = module.incoming_emails.arn
}

output "notification_email_sender_arn" {
  value = aws_ses_email_identity.notification_email_sender.arn
}