data "aws_iam_policy_document" "ecs_task_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "ecs_task_role" {
  name               = var.ecs_task_role_name
  assume_role_policy = data.aws_iam_policy_document.ecs_task_role.json
}


data "aws_iam_policy_document" "dynamo-policy-doc" {
  statement {
    sid       = "AllowDynamoPermissions"
    effect    = "Allow"
    resources = var.ecs_dynamo_policy_resource
    actions   = ["dynamodb:*"]
  }
}

resource "aws_iam_policy" "dynamo-policy" {
  count  = var.dynamo_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.dynamo-policy-doc.json
}

resource "aws_iam_role_policy_attachment" "ecs_task_role_policy_attach_dynamo" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.dynamo_access_enabled ? 1 : 0
  role       = var.ecs_task_role_name
  policy_arn = aws_iam_policy.dynamo-policy[0].arn
}

data "aws_iam_policy_document" "aws_sqs" {
  statement {
    actions = [
      "sqs:*"
    ]
    resources = var.ecs_sqs_policy_resource
  }
}

resource "aws_iam_policy" "aws_sqs" {
  count  = var.sqs_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.aws_sqs.json
}

resource "aws_iam_role_policy_attachment" "aws_sqs" {
  depends_on = [aws_iam_role.ecs_task_role]
  count      = var.sqs_access_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.aws_sqs[0].arn
}

data "aws_iam_policy_document" "aws_sns" {
  statement {
    actions = [
      "sns:Publish",
      "sns:Subscribe",
      "sns:ListSubscriptionsByTopic",
      "sns:SetSubscriptionAttributes"
    ]
    resources = var.ecs_sns_policy_resource
  }
  statement {
    actions = [
      "sns:Publish"
    ]
    not_resources = ["arn:aws:sns:*:*:*"]
  }
  statement {
    actions = [
      "sns:CreatePlatformEndpoint",
      "sns:GetEndpointAttributes"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_policy" "aws_sns" {
  count  = var.sns_access_enabled ? 1 : 0
  policy = data.aws_iam_policy_document.aws_sns.json
}

resource "aws_iam_role_policy_attachment" "aws_sns" {
  count      = var.sns_access_enabled ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.aws_sns[0].arn
}

data "aws_iam_policy_document" "s3" {
  statement {
    effect = "Allow"
    actions = [
      "s3:List*"
    ]
    resources = var.s3_bucket_arns
  }
  statement {
    effect = "Allow"
    actions = [
      "s3:*Object",
      "s3:*ObjectTagging"
    ]
    resources = formatlist("%s/*", var.s3_bucket_arns)
  }
}

resource "aws_iam_policy" "get_object" {
  count  = var.s3_read_objects ? 1 : 0
  policy = data.aws_iam_policy_document.s3.json
}

resource "aws_iam_role_policy_attachment" "get_object" {
  count      = var.s3_read_objects ? 1 : 0
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.get_object[0].arn
}

data "aws_iam_policy_document" "logs" {
  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams",
      "logs:PutLogEventsBatch",
      "logs:DescribeLogStreams"
    ]
    resources = [
      "arn:aws:logs:${var.aws_region}:*:log-group:/ecs/${var.service_name}:*",
      "arn:aws:logs:${var.aws_region}:*:log-group:/logs/${var.service_name}:*"
    ]
  }
  statement {
    actions = [
      "elasticfilesystem:ClientMount",
      "elasticfilesystem:ClientWrite",
      "elasticfilesystem:ClientRootAccess"
    ]
    resources = [
      "arn:aws:elasticfilesystem:us-east-1:381563809177:file-system/fs-04003dc246694565c"
    ]
  }
}

resource "aws_iam_policy" "logs" {
  path        = "/"
  description = "Allow publishing to cloudwatch"

  policy = data.aws_iam_policy_document.logs.json
}

resource "aws_iam_role_policy_attachment" "logs" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.logs.arn
}