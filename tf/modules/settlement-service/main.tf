resource "aws_ecr_repository" "default" {
  image_tag_mutability = "IMMUTABLE"
  name                 = var.ecr_repository_name
  tags                 = var.tags
}

module "execution_role" {
  source = "./modules/execution-role"

  ecs_task_execution_role_name = var.ecs_task_execution_role_name
  secrets_enabled              = true
  secrets_arns = concat(
    var.secrets_arns,
    [aws_secretsmanager_secret.client_credentials.arn]
  )

  service_name = var.service_name
  task_name    = var.task_name
  aws_region   = var.aws_region
}

module "task_role" {
  source = "./modules/task-role"

  service_name               = var.service_name
  task_name                  = var.task_name
  aws_region                 = var.aws_region
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.service_main_table.arn,
    "${aws_dynamodb_table.service_main_table.arn}/*",
    aws_dynamodb_table.service_event_table.arn,
    "${aws_dynamodb_table.service_event_table.arn}/*",
    aws_dynamodb_table.service_lock_table.arn,
    "${aws_dynamodb_table.service_lock_table.arn}/*",
  ]
  ecs_task_role_name      = var.ecs_task_role_name
  dynamo_access_enabled   = var.dynamo_access_enabled
  s3_bucket_arns          = var.s3_bucket_arns
  s3_read_objects         = var.s3_read_objects
  sqs_access_enabled      = var.sqs_access_enabled
  ecs_sqs_policy_resource = var.ecs_sqs_policy_resource
  sns_access_enabled      = true
  ecs_sns_policy_resource = [
    module.settlement_events_sns.topic_arn
  ]
}

module "settlement_events_sns" {
  source = "terraform-aws-modules/sns/aws"
  name   = var.settlement_events_sns_name
}

resource "aws_cloudwatch_log_group" "default" {
  name = "/ecs/${var.service_name}"
  tags = var.tags
}

# ALB Security group
# This is the group you need to edit if you want to restrict access to your application
resource "aws_security_group" "lb" {
  name        = "${var.task_name}-alb"
  description = "controls access to the ALB"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 8080
    to_port     = 8080
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"] # add a CIDR block here
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = var.tags
}

# Traffic to the ECS Cluster should only come from the ALB
resource "aws_security_group" "ecs_tasks" {
  name        = "${var.task_name}-tasks"
  description = "allow inbound access from the ALB only"
  vpc_id      = var.aws_vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.app_port
    to_port         = var.app_port
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = var.tags
}

resource "aws_alb" "main" {
  name            = var.load_balance_name
  subnets         = var.aws_public_subnet_id
  security_groups = [aws_security_group.lb.id]
  access_logs {
    bucket  = var.access_logs_bucket
    enabled = var.access_logs_enabled
  }
  tags = var.tags
}

resource "aws_alb_target_group" "app" {
  name                 = var.alb_target_group_name
  port                 = var.app_port
  protocol             = "HTTPS"
  vpc_id               = var.aws_vpc_id
  target_type          = "ip"
  deregistration_delay = 60

  health_check {
    healthy_threshold   = "3"
    unhealthy_threshold = "10"
    interval            = "120"
    protocol            = "HTTPS"
    matcher             = "200-499"
    timeout             = "50"
    path                = var.health_check_path
  }
  tags = var.tags
}

# Redirect all traffic from the ALB to the target group
resource "aws_alb_listener" "front_end" {
  load_balancer_arn = aws_alb.main.id
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = var.certificate_arn
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  default_action {
    target_group_arn = aws_alb_target_group.app.id
    type             = "forward"
  }
  tags = var.tags
}

resource "aws_ecs_cluster" "main" {
  name = var.cluster_name
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
  tags = var.tags
}

module "service_image_version" {
  source         = "../image_version"
  container_name = var.container_name
  cluster_name   = var.cluster_name
  service_name   = var.service_name
}

resource "aws_ecs_task_definition" "app" {
  family                   = var.task_name
  execution_role_arn       = module.execution_role.arn
  task_role_arn            = module.task_role.arn
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.fargate_cpu
  memory                   = var.fargate_memory
  container_definitions    = templatefile(var.task_definition, {
    app_image                 = "${aws_ecr_repository.default.repository_url}:${module.service_image_version.image_tag}"
    app_port                  = var.app_port
    container_name            = var.container_name
    service_name              = "settlement-service"
    secret_arns               = var.secrets_arns
    environment               = var.environment
    fluent_bit_repository_url = var.fluent_bit_repository_url
  })
  tags = var.tags
}

resource "aws_ecs_service" "main" {
  name             = var.service_name
  cluster          = aws_ecs_cluster.main.id
  task_definition  = aws_ecs_task_definition.app.arn
  desired_count    = var.app_count
  launch_type      = "FARGATE"
  platform_version = "1.4.0"
  tags             = var.tags

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = [var.aws_private_subnet_id[0]]
    assign_public_ip = true
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.app.id
    container_name   = var.container_name
    container_port   = var.app_port
  }

  depends_on = [aws_alb_listener.front_end, module.execution_role.role_policy_attachment_execution_role]
}

resource "aws_cloudwatch_log_group" "service-log-group-dd" {
  name              = "/ecs/${var.service_name}-dd"
  retention_in_days = 7
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "service-log-group" {
  name = "/logs/${var.service_name}"
  tags = var.tags
}


resource "aws_dynamodb_table" "service_main_table" {
  name         = "SettlementService-Main"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "SortKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "SortKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1SortKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1SortKey"
    projection_type = "ALL"
  }

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

resource "aws_dynamodb_table" "service_event_table" {
  name         = "SettlementService-Event"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "SortKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "SortKey"
    type = "N"
  }

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

resource "aws_dynamodb_table" "service_lock_table" {
  name         = "SettlementService-Lock"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "_id"

  attribute {
    name = "_id"
    type = "S"
  }

  tags = merge(
    var.tags,
    {
      Environment = var.environment
    }
  )
}

module "settlement_service_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "settlement-service-dlq"
}

module "settlement_picpay_response" {
  source         = "terraform-aws-modules/sqs/aws"
  version        = "~> 2.0"
  name           = "settlement-picpay-response"
  redrive_policy = "{\"deadLetterTargetArn\":\"${module.settlement_service_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":10}"

  tags = {
    Environment = var.environment
  }
}

module "settlement_friday_response" {
  source         = "terraform-aws-modules/sqs/aws"
  version        = "~> 2.0"
  name           = "settlement-friday-response"
  redrive_policy = "{\"deadLetterTargetArn\":\"${module.settlement_service_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":10}"

  tags = {
    Environment = var.environment
  }
}

module "settlement-friday-request-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-friday-request-alarm"
  alarm_description   = "Settlement - Friday Request Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 3
  datapoints_to_alarm = 3
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-friday-request"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}

module "settlement-friday-query-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-friday-query-alarm"
  alarm_description   = "Settlement - Friday Query Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-friday-query"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-friday-response-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-friday-response-alarm"
  alarm_description   = "Settlement - Friday Response Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-friday-response"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-picpay-request-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-picpay-request-alarm"
  alarm_description   = "Settlement - PicPay Request Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-picpay-request"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}

module "settlement-picpay-query-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-picpay-query-alarm"
  alarm_description   = "Settlement - PicPay Query Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-picpay-query"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-picpay-response-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-picpay-response-alarm"
  alarm_description   = "Settlement - PicPay Response Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 900
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-picpay-response"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-processed-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-processed-alarm"
  alarm_description   = "Settlement - Processed Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 300
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-processed"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P3_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P3_topic_arn]
}

module "settlement-requested-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-requested-alarm"
  alarm_description   = "Settlement - Requested Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 3600
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-requested"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}

module "settlement-validation-alarm" {
  count = var.cloudwatch_alarms_enabled ? 1 : 0

  source  = "terraform-aws-modules/cloudwatch/aws//modules/metric-alarm"
  version = "4.3.0"

  alarm_name          = "settlement-validation-alarm"
  alarm_description   = "Settlement - Validation Queue com atraso"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  threshold           = 300
  period              = 300
  unit                = "Seconds"

  namespace   = "AWS/SQS"
  metric_name = "ApproximateAgeOfOldestMessage"
  statistic   = "Average"

  dimensions = {
    QueueName = "settlement-validation"
  }

  treat_missing_data = "notBreaching"
  alarm_actions      = [var.cloudwatch_alarm_actions_P2_topic_arn]
  ok_actions         = [var.cloudwatch_alarm_actions_P2_topic_arn]
}

resource "aws_secretsmanager_secret" "client_credentials" {
  name = "settlement-service/client-credentials"
}
