module "open_finance_image_version" {
  source         = "../../modules/image_version"
  container_name = "OpenFinance"
  service_name   = "open-finance-service"
  cluster_name   = "open-finance-cluster"
}

resource "aws_secretsmanager_secret" "iniciador" {
  name = "friday/iniciador"
}

resource "aws_dynamodb_table" "open-finance-table" {
  name         = "Friday-OpenFinance"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "RangeKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2RangeKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PartitionKey"
    range_key       = "GSIndex2RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_ecs_cluster" "open_finance_cluster" {
  name ="open-finance-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

module "friday_open_finance_task" {
  source = "../../modules/fargate_task"

  prefix                     = "open-finance"
  ecr_repository_name        = "open-finance"
  fargate_cpu                = var.fargate_cpu
  fargate_memory             = var.fargate_memory
  task_definition            = var.task_definition
  dynamo_access_enabled      = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.open-finance-table.arn,
    "${aws_dynamodb_table.open-finance-table.arn}/*",
    "arn:aws:dynamodb:${var.aws_region}:${var.account_id}:table/Shedlock",
    "arn:aws:dynamodb:${var.aws_region}:${var.account_id}:table/Shedlock/*",
  ]
  s3_read_objects = false
  s3_bucket_arns  = []
  sqs_access_enabled      = true
  ecs_sqs_policy_resource = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:*"]
  sns_access_enabled      = false
  ecs_sns_policy_resource = []
  secrets_enabled        = true
  secrets_arns = [
    aws_secretsmanager_secret.iniciador.arn
  ]
  user_pool_arn_enabled     = false
  app_version               = module.open_finance_image_version.image_tag
  kms_enabled               = true
  kms_key_arns              = [aws_kms_key.open_finance_key.arn]
  fluent_bit_repository_url = var.fluent_bit_repository_url
  aws_region                = var.aws_region
  send_email = false
}

module "open_finance_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = aws_ecs_cluster.open_finance_cluster
  aws_private_subnet_id   = var.private_subnets
  aws_public_subnet_id    = var.public_subnets
  aws_vpc_id              = var.vpc_id
  load_balance_enabled    = true
  prefix                  = "open-finance"
  container_name          = "OpenFinance"
  app_port                = 8443
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.friday_open_finance_task
  certificate_arn_enabled = true
  certificate_arn         = var.certificate_arn
}

module "open_finance_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "open-finance-dlq"

  tags = {
    Environment = var.environment
  }
}

data "aws_iam_policy_document" "open_finance_queue_policy" {
  statement {
    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:open-finance-queue"]
    effect    = "Allow"
    principals {
      type        = "AWS"
      identifiers = [module.friday_open_finance_task.task_role_arn]
    }
  }
}

module "open_finance_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  policy                     = data.aws_iam_policy_document.open_finance_queue_policy.json
  name                       = "open-finance-queue"
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = jsonencode({
    deadLetterTargetArn = module.open_finance_dlq.this_sqs_queue_arn
    maxReceiveCount     = 60
  })
  tags = {
    Environment = var.environment
  }
}

resource "aws_kms_key" "open_finance_key" {
  description         = "KMS Key for encryption/decryption of Open Finance data"
  key_usage           = "ENCRYPT_DECRYPT"
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
}

resource "aws_kms_alias" "open_finance_key_alias" {
  name          = "alias/open-finance-key"
  target_key_id = aws_kms_key.open_finance_key.key_id
}

resource "aws_dynamodb_table" "finance_data_table" {
  name         = "Friday-OpenFinanceData"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "RangeKey"

  point_in_time_recovery {
    enabled = true
  }

  ttl {
    attribute_name = "ExpirationTTL"
    enabled        = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex2RangeKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSIndex2"
    hash_key        = "GSIndex2PartitionKey"
    range_key       = "GSIndex2RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}