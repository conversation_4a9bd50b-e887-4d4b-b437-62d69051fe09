variable "environment" {
  description = "The environment"
}

variable "aws_region" {
  description = "The AWS Region"
}

variable "fluent_bit_repository_url" {
  description = "FluentBit repository url"
}

variable "task_definition" {
  description = "Task Definition file"
}

variable "vpc_id" {
  description = "vpc_id"
}

variable "private_subnets" {
  description = "private_subnets"
}

variable "public_subnets" {
  description = "public_subnets"
}

variable "certificate_arn" {
  description = "certificate_arn"
}

variable "account_id" {
  description = "account_id"
}

variable "client_task_role_arn" {
  description = "client_task_role_arn"
}

variable "fargate_cpu" {
  description = "fargate_cpu"
  default = 1024
}

variable "fargate_memory" {
  description = "fargate_memory"
  default = 2048
}