resource "aws_secretsmanager_secret" "debito_direto" {
  name = "friday/debito-direto"
}

resource "aws_secretsmanager_secret_version" "debito-direto-initialization" {
  secret_id = aws_secretsmanager_secret.debito_direto.id
  secret_string = jsonencode({
    "BASE_URI"         = "REPLACE"
    "CLIENT_ID"        = "REPLACE"
    "CLIENT_SECRET"    = "REPLACE"
  })

  lifecycle {
    ignore_changes = [secret_string, version_stages]
  }
}