locals {
  service_name   = "debitos-veiculares"
  conainer_name  = "DebitosVeiculares"
  image_tag      = var.first_run ? "FIRST_RUN" : module.debitos_veiculares_image_version[0].image_tag
  ecr_repository = "${var.account_id}.dkr.ecr.${var.aws_region}.amazonaws.com/${local.service_name}:${local.image_tag}"
}

resource "aws_dynamodb_table" "debitos-veiculares-table" {
  name         = "Debitos-Veiculares"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PK"
  range_key    = "SK"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PK"
    type = "S"
  }

  attribute {
    name = "SK"
    type = "S"
  }

  attribute {
    name = "GSI1PK"
    type = "S"
  }

  attribute {
    name = "GSI1SK"
    type = "S"
  }

  attribute {
    name = "GSI2PK"
    type = "S"
  }

  attribute {
    name = "GSI2SK"
    type = "S"
  }

  attribute {
    name = "LSI1SK"
    type = "S"
  }

  attribute {
    name = "LSI2SK"
    type = "S"
  }

  attribute {
    name = "LSI3SK"
    type = "S"
  }

  attribute {
    name = "LSI4SK"
    type = "S"
  }

  attribute {
    name = "LSI5SK"
    type = "S"
  }

  local_secondary_index {
    name            = "LSI1"
    projection_type = "ALL"
    range_key       = "LSI1SK"
  }

  local_secondary_index {
    name            = "LSI2"
    projection_type = "ALL"
    range_key       = "LSI2SK"
  }

  local_secondary_index {
    name            = "LSI3"
    projection_type = "ALL"
    range_key       = "LSI3SK"
  }

  local_secondary_index {
    name            = "LSI4"
    projection_type = "ALL"
    range_key       = "LSI4SK"
  }

  local_secondary_index {
    name            = "LSI5"
    projection_type = "ALL"
    range_key       = "LSI5SK"
  }

  global_secondary_index {
    name            = "GSI1"
    hash_key        = "GSI1PK"
    range_key       = "GSI1SK"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  global_secondary_index {
    name            = "GSI2"
    hash_key        = "GSI2PK"
    range_key       = "GSI2SK"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

module "debitos_veiculares_task" {
  source = "../../modules/fargate_task"

  prefix                = local.service_name
  ecr_repository_name   = local.service_name
  fargate_cpu           = var.fargate_cpu
  fargate_memory        = var.fargate_memory
  task_definition       = "${path.module}/debitos-veiculares.json"
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.debitos-veiculares-table.arn,
    "${aws_dynamodb_table.debitos-veiculares-table.arn}/*",
    "arn:aws:dynamodb:${var.aws_region}:${var.account_id}:table/Shedlock",
    "arn:aws:dynamodb:${var.aws_region}:${var.account_id}:table/Shedlock/*",
  ]
  secrets_enabled = true
  secrets_arns = concat(var.secrets, [aws_secretsmanager_secret.debito_direto.arn])
  #secrets_arns    = var.secrets
  secrets_map = {
    # datadog credentials
    "DD_API_KEY" = "${var.secrets[0]}:DD_API_KEY::"
    # debito direto credentials
    "DD_BASE_URI"      = "${aws_secretsmanager_secret.debito_direto.arn}:BASE_URI::"
    "DD_CLIENT_ID"     = "${aws_secretsmanager_secret.debito_direto.arn}:CLIENT_ID::"
    "DD_CLIENT_SECRET" = "${aws_secretsmanager_secret.debito_direto.arn}:CLIENT_SECRET::"
  }
  app_version           = local.image_tag
  aws_region            = var.aws_region
  sqs_access_enabled    = true
  ecs_sqs_policy_resource = ["arn:aws:sqs:${var.aws_region}:${var.account_id}:*"]
  s3_read_objects       = true
  s3_bucket_arns        = var.s3_bucket_arns
  ephemeral_storage     = 0
  user_pool_arn_enabled = false

  aditional_container_definitions = {
    account_id     = var.account_id
    region         = var.aws_region
    container_name = local.conainer_name
    app_image      = local.ecr_repository
    environment    = var.environment
    app_port       = 8443
    service_name   = local.service_name
  }
}

module "debitos_veiculares" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = var.cluster
  aws_private_subnet_id   = var.private_subnets
  aws_public_subnet_id    = var.public_subnets
  aws_vpc_id              = var.vpc_id
  load_balance_enabled    = true
  prefix                  = local.service_name
  container_name          = local.conainer_name
  app_port                = 8443
  target_group_protocol   = "HTTP"
  app_count               = 1
  health_check_path       = "/actuator/health"
  task_definition         = module.debitos_veiculares_task
  certificate_arn_enabled = true
  certificate_arn         = var.certificate_arn
  alb_idle_timeout        = 900
}

module "debitos_veiculares_image_version" {
  source         = "../../modules/image_version"
  count          = var.first_run ? 0 : 1
  container_name = local.conainer_name
  service_name   = "${local.service_name}-service"
}