module "debitos-veiculares-enrollment-user_queue" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "~> 2.0"
  name                       = "debitos-veiculares-enrollment-user"
  delay_seconds              = 0
  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 300
  redrive_policy             = "{\"deadLetterTargetArn\":\"${module.debitos-veiculares-enrollment-user_dlq.this_sqs_queue_arn}\",\"maxReceiveCount\":3}"

  tags = {
    Environment = var.environment
  }
}

module "debitos-veiculares-enrollment-user_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "debitos-veiculares-enrollment-user_dlq"

  tags = {
    Environment = var.environment
  }
}