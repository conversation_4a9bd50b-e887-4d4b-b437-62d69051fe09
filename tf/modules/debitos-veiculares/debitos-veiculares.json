[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "essential": true,
    "environment" : [
      {
        "name": "SPRING_PROFILES_ACTIVE",
        "value": "${environment}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "DD_VERSION",
        "value": "${app_version}"
      }
    ],
    "dockerLabels": {
          "com.datadoghq.tags.env": "${environment}",
          "com.datadoghq.tags.service": "${service_name}",
          "com.datadoghq.tags.version": "${app_version}"
        },
    "secrets": [
      %{ for name, value in secrets_map }
        {
          "name": "${name}",
          "valueFrom": "${value}"
        }
        %{if name != keys(secrets_map)[length(keys(secrets_map))-1]}
        ,
        %{endif}
      %{ endfor ~}
    ],
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port}
    }
    ],
    "volumesFrom": [],
    "logConfiguration": {
          "logDriver":"awsfirelens",
          "options": {
            "Name": "cloudwatch",
            "region": "us-east-1",
            "log_group_name": "/ecs/${service_name}-task",
            "auto_create_group": "false",
              "log_stream_name": "ecs/${service_name}/$(ecs_task_id)"
          }
        }
  },
  {
    "name": "datadog-agent",
    "image": "${account_id}.dkr.ecr.${region}.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "secrets": [
      {
      "name": "DD_API_KEY",
      "valueFrom": "${secrets_map["DD_API_KEY"]}"
      }
    ],
    "environment" : [
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${service_name}-task-dd",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "image": "public.ecr.aws/aws-observability/aws-for-fluent-bit:init-latest",
    "name": "log_router",
    "essential": true,
    "cpu":0,
    "portMappings"          : [],
    "user"                  : "0",
    "volumesFrom"           : [],
    "firelensConfiguration": {
    "type": "fluentbit"
  },
  "secrets": [
    {
    "name": "DD_API_KEY",
    "valueFrom": "${secrets_map["DD_API_KEY"]}"
    }
  ],
  "environment" : [
    {
      "name": "SERVICE_CONTAINER_NAME",
      "value": "${container_name}"
    },
    {
      "name": "DD_ENV",
      "value": "${environment}"
    },
    {
      "name": "DD_SERVICE",
      "value": "${service_name}"
    },
    {
      "name": "LOG_GROUP_NAME",
      "value": "/logs/${service_name}-task"
    },
    {
      "name": "REGION",
      "value": "${region}"
    },
    {
      "name": "aws_fluent_bit_init_s3_1",
      "value": "arn:aws:s3:::${account_id}-config-files/config/general.conf"
    },
    {
      "name": "aws_fluent_bit_init_s3_2",
      "value": "arn:aws:s3:::${account_id}-config-files/config/parsers.conf"
    }
  ],
  "logConfiguration": {
    "logDriver": "awslogs",
    "options": {
      "awslogs-group": "/ecs/${service_name}-task-dd",
      "awslogs-region": "${region}",
      "awslogs-stream-prefix": "ecs"
    }
  },
  "memoryReservation": 50
  }
]
