variable "environment" {
  description = "The environment"
}

variable "cluster" {
  description = "cluster name"
}

variable "aws_region" {
  description = "The AWS Region"
}

variable "vpc_id" {
  description = "vpc_id"
}

variable "private_subnets" {
  description = "private_subnets"
}

variable "public_subnets" {
  description = "public_subnets"
}

variable "account_id" {
  description = "account_id"
}

variable "fargate_cpu" {
  description = "fargate_cpu"
  default = 1024
}

variable "fargate_memory" {
  description = "fargate_memory"
  default = 2048
}

variable "secrets" {
  description = "secrets"
  default = []
}

variable "certificate_arn" {
    description = "certificate_arn"
    default = null
}

variable "s3_bucket_arns" {
    type = list(string)
    default = []
}

variable "first_run" {
  description = "first_run"
  type = bool
  default = false
}