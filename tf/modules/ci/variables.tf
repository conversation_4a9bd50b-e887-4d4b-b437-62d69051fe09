variable account_id {
  validation {
    condition     = contains(["************", "************"], var.account_id)
    error_message = "Valid values for var: test_variable are (************, ************)."
  }
}

variable cf_distributions {
  description = "cloudfront distributions that will be modified by ci"
  type        = list(string)
}

variable environment {
  validation {
    condition     = contains(["staging", "production"], var.environment)
    error_message = "Valid values for var: test_variable are (staging, production)."
  }
}