locals {
  account_id       = var.account_id
  cf_distributions = var.cf_distributions
  ci_policy_name   = "GitLabCiPipelinePolicy"
  role_suffixes    = ["-execution-role", "ExecutionRole", "-task-role"]
  s3_buckets       = [
    "notification-templates-${var.environment}",
    "via1-notification-templates-${var.environment}",
    "via1-static-lambda-proxy-${var.environment}",
    "via1-webapp-${var.environment}",
    "via1-website-${var.environment}",
    "${var.account_id}-dda-files-lambda"
  ]
  lambda_names = [
    "static-lambda-proxy-origin-request",
    "static-lambda-proxy-origin-response",
    "dda-files"
  ]
  # lambda_layer_names = []

}

data "aws_iam_policy_document" "gitlab_ci_pipeline_policy" {
  statement {
    actions   = ["iam:PassRole"]
    resources = [for suffix in local.role_suffixes :"arn:aws:iam::${local.account_id}:role/*${suffix}"]
  }

  statement {
    actions = [
      "s3:ListBucketVersions",
      "s3:ReplicateTags",
      "s3:ListBucket",
      "s3:PutObject",
      "s3:GetObject",
      "s3:PutBucketTagging",
      "s3:GetObjectTagging",
      "s3:PutObjectTagging",
      "s3:DeleteObject"
    ]
    resources = concat(
      [for bucket in local.s3_buckets :"arn:aws:s3:::${bucket}"],
      [for bucket in local.s3_buckets :"arn:aws:s3:::${bucket}/*"]
    )
  }

  statement {
    actions = [
      "s3:ListAllMyBuckets",
      "ecr:BatchCheckLayerAvailability",
      "ecr:CompleteLayerUpload",
      "ecr:GetAuthorizationToken",
      "ecr:InitiateLayerUpload",
      "ecr:PutImage",
      "ecr:UploadLayerPart",
      "ecs:DescribeTaskDefinition",
      "ecs:RegisterTaskDefinition",
      "ecs:UpdateService",
    ]
    resources = ["*"]
  }

  statement {
    actions = [
      "lambda:EnableReplication",
      "lambda:GetFunctionConfiguration",
      "lambda:GetFunction",
      "lambda:UpdateFunctionCode",
      "lambda:UpdateFunctionConfiguration"
    ]
    resources = concat(
      [for lambda_name in local.lambda_names :"arn:aws:lambda:us-east-1:${local.account_id}:function:${lambda_name}"],
      [for lambda_name in local.lambda_names :"arn:aws:lambda:us-east-1:${local.account_id}:function:${lambda_name}:*"]
    )
  }

  # statement {
  #   actions = [
  #     "lambda:PublishLayerVersion",
  #     "lambda:GetLayerVersion"
  #   ]
  #   resources = concat(
  #     [for lambda_layer_name in local.lambda_layer_names :"arn:aws:lambda:us-east-1:${local.account_id}:layer:${lambda_layer_name}"],
  #     [for lambda_layer_name in local.lambda_layer_names :"arn:aws:lambda:us-east-1:${local.account_id}:layer:${lambda_layer_name}:*"]
  #   )
  # }

  statement {
    actions = [
      "cloudfront:GetDistributionConfig",
      "cloudfront:UpdateDistribution",
    ]
    resources = [
      for dist in local.cf_distributions :"arn:aws:cloudfront::${local.account_id}:distribution/${dist}"
    ]
  }
}

resource "aws_iam_policy" "gitlab_ci_pipeline_policy" {
  name   = local.ci_policy_name
  path   = "/"
  policy = data.aws_iam_policy_document.gitlab_ci_pipeline_policy.json
}
