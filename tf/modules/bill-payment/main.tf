resource "aws_s3_bucket" "public_bucket" {
  count = var.create_public_bucket ? 1 : 0
  bucket   = var.public_bucket_name
  acl      = "public-read"

  versioning {
    enabled = true
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET"]
    allowed_origins = ["https://use.friday.ai", "https://use.via1.app", "https://use.meupagador.com.br"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket_object" "termo_de_uso_2021_04_05" {
  count = var.create_public_bucket ? 1 : 0
  bucket = var.public_bucket_name
  key    = "termo_de_uso/termo_de_uso_via1_2021_04_05.pdf"
  source = "../../files/termo_de_uso/termo_de_uso_via1_2021_04_05.pdf"
  etag = filemd5("../../files/termo_de_uso/termo_de_uso_via1_2021_04_05.pdf")
  acl = "public-read"
  content_type = "application/pdf"
}

resource "aws_s3_bucket_object" "termo_de_uso_2021_05_28" {
  count = var.create_public_bucket ? 1 : 0
  bucket = var.public_bucket_name
  key    = "termo_de_uso/termo_de_uso_via1_2021_05_28.pdf"
  source = "../../files/termo_de_uso/termo_de_uso_via1_2021_05_28.pdf"
  etag = filemd5("../../files/termo_de_uso/termo_de_uso_via1_2021_05_28.pdf")
  acl = "public-read"
  content_type = "application/pdf"
}

resource "aws_s3_bucket_object" "termo_de_uso_friday_2021_10_28" {
  count = var.create_public_bucket ? 1 : 0
  bucket = var.public_bucket_name
  key    = "termo_de_uso/termo_de_uso_friday_2021_10_28.pdf"
  source = "../../files/termo_de_uso/termo_de_uso_friday_2021_10_28.pdf"
  etag = filemd5("../../files/termo_de_uso/termo_de_uso_friday_2021_10_28.pdf")
  acl = "public-read"
  content_type = "application/pdf"
}

resource "aws_s3_bucket_object" "termo_de_uso_friday_2022_03_03" {
  count = var.create_public_bucket ? 1 : 0
  bucket = var.public_bucket_name
  key    = "termo_de_uso/termo_de_uso_friday_2022_03_03.pdf"
  source = "../../files/termo_de_uso/termo_de_uso_friday_2022_03_03.pdf"
  etag = filemd5("../../files/termo_de_uso/termo_de_uso_friday_2022_03_03.pdf")
  acl = "public-read"
  content_type = "application/pdf"
}

resource "aws_s3_bucket" "user_receipts" {
  bucket   = var.user_receipts_bucket_name

  # Prevent accidental deletion of this S3 bucket
  lifecycle {
    prevent_destroy = true
  }

  lifecycle_rule {
    enabled = true
    expiration {
      days = var.user_receipts_object_expiration
    }
  }

  versioning {
    enabled    = false
    mfa_delete = false
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET"]
    allowed_origins = ["https://use.friday.ai", "https://use.via1.app", "https://use.meupagador.com.br"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }

  # Enable server-side encryption by default
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
}