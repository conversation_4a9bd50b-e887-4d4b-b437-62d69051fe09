module "ai_chatbot_image_version" {
  count          = var.first_run ? 0 : 1
  source         = "../../modules/image_version"
  container_name = "AIChatbot"
  service_name   = "ai-chatbot-service"
  cluster_name   = "ai-chatbot-cluster"
}

locals {
  image_tag = var.first_run ? "FIRST_RUN" : module.ai_chatbot_image_version[0].image_tag
}

resource "aws_dynamodb_table" "chatbot_history" {
  name         = "Friday-AiChatHistory"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "RangeKey"

  point_in_time_recovery {
    enabled = true
  }

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "RangeKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1PartitionKey"
    type = "S"
  }

  attribute {
    name = "GSIndex1RangeKey"
    type = "S"
  }

  global_secondary_index {
    name            = "GSIndex1"
    hash_key        = "GSIndex1PartitionKey"
    range_key       = "GSIndex1RangeKey"
    projection_type = "ALL"
    read_capacity   = 0
    write_capacity  = 0
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_ecs_cluster" "ai_chatbot_cluster" {
  name = "ai-chatbot-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

module "friday_ai_chatbot_task" {
  source = "../../modules/fargate_task"

  prefix                = "ai-chatbot"
  ecr_repository_name   = "ai-chatbot"
  fargate_cpu           = 1024
  fargate_memory        = 4096
  task_definition       = var.task_definition
  dynamo_access_enabled = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.chatbot_history.arn,
    "${aws_dynamodb_table.chatbot_history.arn}/*",
    var.billpayment_table_arn,
    "${var.billpayment_table_arn}/*",
    var.shedlock_table_arn,
    "${var.shedlock_table_arn}/*",
  ]
  s3_read_objects           = true
  s3_bucket_arns            = var.s3_bucket_arns
  sqs_access_enabled        = true
  ecs_sqs_policy_resource   = var.ecs_sqs_list_policy_resource
  sns_access_enabled        = false
  ecs_sns_policy_resource = []
  textract_enabled          = true
  secrets_enabled           = true
  secrets_map               = var.secrets_arns
  secrets_arns              = [for secret_arn in var.secrets_arns :secret_arn]
  user_pool_arn_enabled     = false
  app_version               = local.image_tag
  kms_enabled               = true
  kms_key_arns               = [var.cognito_custom_sender_kms_key_arn]
  fluent_bit_repository_url = var.fluent_bit_repository_url
  aws_region                = var.aws_region
  send_email                = true
  transcribe_audio_enabled  = true
}

module "ai_chatbot_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = aws_ecs_cluster.ai_chatbot_cluster
  aws_private_subnet_id   = var.private_subnets
  aws_public_subnet_id    = var.public_subnets
  aws_vpc_id              = var.vpc_id
  load_balance_enabled    = true
  prefix                  = "ai-chatbot"
  container_name          = "AIChatbot"
  app_port                = 8443
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.friday_ai_chatbot_task
  certificate_arn_enabled = true
  certificate_arn         = var.certificate_arn
}


# INICIO FILA INTEGRACAO ENVIO COGNITO TOKEN

resource "aws_sqs_queue" "chatbot_cognito_token_dlq" {
  name                      = "chatbot_cognito_token_dlq"
  delay_seconds             = 0
  max_message_size         = 262144
  message_retention_seconds = 1209600  # 14 dias
  receive_wait_time_seconds = 20
  visibility_timeout_seconds = 30

  tags = {
    Environment = var.environment
  }
}

resource "aws_sqs_queue" "chatbot_cognito_token" {
  name                      = "chatbot_cognito_token"
  delay_seconds             = 0
  max_message_size         = 262144
  message_retention_seconds = 345600  # 4 dias
  receive_wait_time_seconds = 20
  visibility_timeout_seconds = 30
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.chatbot_cognito_token_dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Environment = var.environment
  }
}

# Política que permite o SNS publicar mensagens na fila
resource "aws_sqs_queue_policy" "chatbot_cognito_token_policy" {
  queue_url = aws_sqs_queue.chatbot_cognito_token.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action = "sqs:SendMessage"
        Resource = aws_sqs_queue.chatbot_cognito_token.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = var.cognito_token_sns_topic_arn
          }
        }
      }
    ]
  })
}

# Assinatura do tópico SNS do Cognito para a fila do chatbot
resource "aws_sns_topic_subscription" "chatbot_cognito_token" {
  topic_arn = var.cognito_token_sns_topic_arn
  protocol  = "sqs"
  endpoint  = aws_sqs_queue.chatbot_cognito_token.arn
  raw_message_delivery = true
}