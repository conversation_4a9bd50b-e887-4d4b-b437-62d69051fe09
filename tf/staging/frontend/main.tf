locals {
  account_id                    = "************"
  production_availability_zones = ["us-east-1a", "us-east-1b"]
}

provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [local.account_id]
}

module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "via1-staging-infrastructure"
  dynamodb_table_name = "frontend-infrastructure-lock-table"
}

terraform {
  backend "s3" {
    bucket         = "via1-staging-infrastructure"
    key            = "frontend/terraform.tfstate"
    dynamodb_table = "frontend-infrastructure-lock-table"
    encrypt        = true
    region         = "us-east-1"
  }
}

resource "aws_cloudfront_origin_access_identity" "webapp_origin_access_identity" {
  comment = "Webapp Origin Access Identity"
}

resource "aws_s3_bucket" "webapp_staging" {
  bucket = "via1-webapp-staging"
  acl    = "private"
  policy = templatefile("bucket_policy.json", {
    user    = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.iam_arn
    bucket  = "via1-webapp-staging"
    pattern = "current/*"
  })
  lifecycle {
    prevent_destroy = true
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "webapp_staging" {
  bucket = aws_s3_bucket.webapp_staging.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "webapp_staging" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.webapp_staging.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.webapp_staging.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.webapp_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.webapp_staging.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:2"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:2"
    }
  }
  aliases = ["use.meupagador.com.br"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  custom_error_response {
    error_code            = 403
    error_caching_min_ttl = 300
    response_code         = 200
    response_page_path    = "/index.html"
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudfront_origin_access_identity" "site_origin_access_identity" {
  comment = "Site Origin Access Identity"
}

resource "aws_s3_bucket" "site_staging" {
  bucket = "via1-website-staging"
  acl    = "private"
  policy = templatefile("bucket_policy.json", {
    user    = aws_cloudfront_origin_access_identity.site_origin_access_identity.iam_arn
    bucket  = "via1-website-staging"
    pattern = "current/*"
  })
  lifecycle {
    prevent_destroy = false
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "site_staging" {
  bucket = aws_s3_bucket.site_staging.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "site_staging" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.site_staging.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.site_staging.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.site_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.site_staging.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:2"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:2"
    }
  }
  aliases = ["meupagador.com.br", "www.meupagador.com.br"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  custom_error_response {
    error_caching_min_ttl = 300
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_cloudfront_origin_access_identity" "notification_templates_origin_access_identity" {
  comment = "Notification Templates Identity"
}

resource "aws_s3_bucket" "notification_templates_staging" {
  bucket = "via1-notification-templates-staging"
  acl    = "private"
  policy = templatefile("bucket_policy.json", {
    user    = aws_cloudfront_origin_access_identity.notification_templates_origin_access_identity.iam_arn
    bucket  = "via1-notification-templates-staging"
    pattern = "current/static/*"
  })
  lifecycle {
    prevent_destroy = false
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "notification_templates_staging" {
  bucket = aws_s3_bucket.notification_templates_staging.id

  block_public_acls   = true
  block_public_policy = true
}

resource "aws_cloudfront_distribution" "notification_templates_staging" {
  // origin is where CloudFront gets its content from.
  origin {
    domain_name = aws_s3_bucket.notification_templates_staging.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.notification_templates_staging.bucket}"
    origin_path = "/current"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.notification_templates_origin_access_identity.cloudfront_access_identity_path
    }
  }
  enabled             = true
  default_root_object = "index.html"
  // All values are defaults from the AWS console.
  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    // This needs to match the `origin_id` above.
    target_origin_id = "S3-${aws_s3_bucket.notification_templates_staging.bucket}"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    lambda_function_association {
      event_type   = "origin-request"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_request.arn}:2"
    }
    lambda_function_association {
      event_type   = "origin-response"
      include_body = false
      lambda_arn   = "${aws_lambda_function.static_lambda_proxy_origin_response.arn}:2"
    }
  }
  aliases = ["notification-templates-cdn.meupagador.com.br"]
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  // Here's where our certificate is loaded in!
  viewer_certificate {
    acm_certificate_arn      = var.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.1_2016"
  }
  tags = {
    Environment = var.environment
  }
}


/*
 * Lambda@Edge Static Proxies - Bucket
 */

resource "aws_s3_bucket" "static_lambda_proxy_staging" {
  bucket = "via1-static-lambda-proxy-staging"
  acl    = "private"
  lifecycle {
    prevent_destroy = false
  }
  versioning {
    enabled = true
  }
  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "static_lambda_proxy_staging" {
  bucket = aws_s3_bucket.static_lambda_proxy_staging.id

  block_public_acls   = true
  block_public_policy = true
}


/*
 * Lambda@Edge Static Proxies - Origin Request
 */

data "aws_iam_policy_document" "static_lambda_execution_role" {
  version = "2012-10-17"
  statement {
    sid     = ""
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com", "edgelambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "static_lambda_proxy_origin_request" {
  name               = "static_lambda_proxy_origin_request"
  assume_role_policy = data.aws_iam_policy_document.static_lambda_execution_role.json
}

resource "aws_cloudwatch_log_group" "static_lambda_proxy_origin_request" {
  name              = "/aws/lambda/static-lambda-proxy-origin-request"
  retention_in_days = 14
}

resource "aws_iam_policy" "static_lambda_proxy_origin_request_logging" {
  name        = "static-lambda-proxy-origin-request-logging"
  path        = "/"
  description = "IAM policy for logging from a static lambda proxy for origin request"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${var.aws_region}:*:log-group:/aws/lambda/static-lambda-proxy-origin-request:*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "static_lambda_proxy_origin_request_logging" {
  role       = aws_iam_role.static_lambda_proxy_origin_request.name
  policy_arn = aws_iam_policy.static_lambda_proxy_origin_request_logging.arn
}

// TODO: there is no assurance lambda is on correct region
resource "aws_lambda_function" "static_lambda_proxy_origin_request" {
  function_name = "static-lambda-proxy-origin-request"
  role          = aws_iam_role.static_lambda_proxy_origin_request.arn
  handler       = "index.handler"
  runtime       = "nodejs10.x"
  s3_bucket     = aws_s3_bucket.static_lambda_proxy_staging.id
  s3_key        = "current/origin-request-handler.zip"
  tags = {
    Environment = var.environment
  }
  depends_on = [aws_iam_role_policy_attachment.static_lambda_proxy_origin_request_logging,
    aws_cloudwatch_log_group.static_lambda_proxy_origin_request]
}

/*
 * Lambda@Edge Static Proxies - Origin Response
 */

resource "aws_iam_role" "static_lambda_proxy_origin_response" {
  name               = "static_lambda_proxy_origin_response"
  assume_role_policy = data.aws_iam_policy_document.static_lambda_execution_role.json
}

resource "aws_cloudwatch_log_group" "static_lambda_proxy_origin_response" {
  name              = "/aws/lambda/static-lambda-proxy-origin-response"
  retention_in_days = 14
}

resource "aws_iam_policy" "static_lambda_proxy_origin_response_logging" {
  name        = "static-lambda-proxy-origin-response-logging"
  path        = "/"
  description = "IAM policy for logging from a static lambda proxy for origin response"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${var.aws_region}:*:log-group:/aws/lambda/static-lambda-proxy-origin-response:*",
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "static_lambda_proxy_origin_response_logging" {
  role       = aws_iam_role.static_lambda_proxy_origin_response.name
  policy_arn = aws_iam_policy.static_lambda_proxy_origin_response_logging.arn
}

// TODO: there is no assurance lambda is on correct region
resource "aws_lambda_function" "static_lambda_proxy_origin_response" {
  function_name = "static-lambda-proxy-origin-response"
  role          = aws_iam_role.static_lambda_proxy_origin_response.arn
  handler       = "index.handler"
  runtime       = "nodejs10.x"
  s3_bucket     = aws_s3_bucket.static_lambda_proxy_staging.id
  s3_key        = "current/origin-response-handler.zip"
  tags = {
    Environment = var.environment
  }
  depends_on = [aws_iam_role_policy_attachment.static_lambda_proxy_origin_response_logging,
    aws_cloudwatch_log_group.static_lambda_proxy_origin_response]
}
