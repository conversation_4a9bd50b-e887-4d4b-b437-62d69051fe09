output "public_subnets_id" {
  value = module.vpc.public_subnets
}

output "private_subnets_id" {
  value = module.vpc.private_subnets
}

output "security_group" {
  value = module.vpc.default_security_group_id
}

output "user_table_arn" {
  value = aws_dynamodb_table.user_table.arn
}

output "nat_gateway_ips" {
  value = module.vpc.nat_public_ips
}

output "bill_payment_api_alb_hostname" {
  value = module.bill_payment_service.alb_hostname
}

output "bill_payment_api_alb_alb_zone_id" {
  value = module.bill_payment_service.alb_zone_id
}

output "dda_bills_alb_hostname" {
  value = module.dda_bills_service.alb_hostname
}

output "dda_bills_alb_alb_zone_id" {
  value = module.dda_bills_service.alb_zone_id
}

output "user_pool_domain_cloudfront_distribution_arn" {
  value = module.via1_cognito.user_pool_domain_cloudfront_distribution_arn
}

output "wallet_event_sns_topic_arn" {
  value = module.wallet_events_sns.topic_arn
}

output "incoming_emails_sns_arn" {
  value = module.incoming_emails.topic_arn
}

output "incoming_emails_s3_arn" {
  value = module.ses-email-receiver.incoming_emails_bucket_arn
}