[{"name": "OpenFinance", "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/open-finance:${app_version}", "cpu": 1014, "memory": 1742, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "staging"}, {"name": "JAVA_OPTS", "value": "-Xmx1048m"}], "secrets": [{"name": "INICIADOR_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:friday/iniciador-fPvozH:CLIENT_ID::"}, {"name": "INICIADOR_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:friday/iniciador-fPvozH:CLIENT_SECRET::"}], "dockerLabels": {"com.datadoghq.tags.env": "staging", "com.datadoghq.tags.service": "open-finance"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/open-finance-task", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]