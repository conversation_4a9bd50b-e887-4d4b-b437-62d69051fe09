[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": 502,
    "memory": 1792,
    "essential": true,
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "friday,${environment}"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port}
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${service_name}",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  }
]
