[
  {
    "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/bill-payment-api:${app_version}",
    "cpu": 246,
    "memory": 1792,
    "essential": true,
    "stopTimeout": 120,
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "friday,staging"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "staging",
      "com.datadoghq.tags.service": "bill-payment-service",
      "com.datadoghq.tags.version": "${app_version}"
    },
    "secrets": [
      %{ for env, value in secrets_map }
      {
        "name": "${env}",
        "valueFrom": "${value}"
      },
      %{ endfor ~}
      {
        "name": "COMMUNICATION_CENTRE_INTEGRATION_BLIP_AUTH",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/blip-password-0mRtxf"
      },
      {
        "name": "INTEGRATIONS_ARBI_CLIENT_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/arbi-credentials-7OX195:CLIENT_SECRET::"
      },
      {
        "name": "INTEGRATIONS_ARBI_USER_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/arbi-credentials-7OX195:USER_TOKEN::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_CLIENT",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/arbi-ecm-credentials-ikkkUU:CLIENT::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_API_KEY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/arbi-ecm-credentials-ikkkUU:API_KEY::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/arbi-ecm-credentials-ikkkUU:USERNAME::"
      },
      {
        "name": "INTEGRATIONS_ARBI_ECM_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/arbi-ecm-credentials-ikkkUU:PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_USERPILOT_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/userpilot-hrCVU6:TOKEN::"
      },
      {
        "name": "ARBI_CALLBACK_IDENTITY",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:ARBI_CALLBACK_IDENTITY::"
      },
      {
        "name": "ARBI_CALLBACK_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:ARBI_CALLBACK_SECRET::"
      },
      {
        "name": "INTEGRATIONS_QUOD_USERNAME",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:QUOD_USERNAME::"
      },
      {
        "name": "INTEGRATIONS_QUOD_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:QUOD_PASSWORD::"
      },
      {
        "name": "MICRONAUT_HTTP_SERVICES_ARBI_SSL_KEY_STORE_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:ARBI_MTLS_PASSWORD::"
      },
      {
        "name": "INTEGRATIONS_BIGDATACORP_ACCESS_TOKEN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:via1/bigdatacorp-credentials-UaDCK5:ACCESS_TOKEN::"
      },
      {
        "name": "MODATTA_B2B_SECRET",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:MODATTA_B2B_SECRET::"
      },
      {
        "name": "INTEGRATIONS_FIREBASE_CLOUD_MESSAGING_JSON",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:bill-payment-api/auth_secrets-F2bIfp:FIREBASE_CLOUD_MESSAGING_JSON::"
      },
      {
        "name": "INTEGRATIONS_BIGDATACORP_LOGIN",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:via1/bigdatacorp-credentials-UaDCK5:LOGIN::"
      },
      {
        "name": "INTEGRATIONS_BIGDATACORP_PASSWORD",
        "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:via1/bigdatacorp-credentials-UaDCK5:PASSWORD::"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8443,
        "hostPort": 8443
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/bill-payment-task",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  }
]