[
  {
    "name": "AIChatbot",
    "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/ai-chatbot:${app_version}",
    "cpu": 1014,
    "memory": 3789,
    "essential": true,
    "environment": [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "friday,staging"
      },
      {
        "name": "JAVA_OPTS",
        "value": "-Xmx2048m"
      },
      {
        "name": "DD_ENV",
        "value": "staging"
      },
      {
        "name": "DD_SERVICE",
        "value": "ai-chatbot-service"
      }
      ],
    "secrets": [
      %{ for name, value in secrets_map }
        {
          "name": "${name}",
          "valueFrom": "${value}"
        }
        %{if name != keys(secrets_map)[length(keys(secrets_map))-1]}
        ,
        %{endif}
        %{ endfor ~}
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "staging",
      "com.datadoghq.tags.service": "ai-chatbot"
    },
    "portMappings": [
      {
        "containerPort": 8443,
        "hostPort": 8443
      }
    ],
    "logConfiguration": {
      "logDriver": "awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/ai-chatbot-task",
        "auto_create_group": "false",
        "log_stream_name": "ecs/ai-chatbot-task/$(ecs_task_id)"
      }
    }
  },
  {
    "name": "datadog-agent",
    "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/ecr-public/datadog/agent:7.49.1",
    "cpu": 10,
    "memory": 256,
    "essential": true,
    "environment": [
      {
        "name": "DD_API_KEY",
        "value": "********************************"
      },
      {
        "name": "DD_SITE",
        "value": "datadoghq.com"
      },
      {
        "name": "ECS_FARGATE",
        "value": "true"
      }
    ],
    "portMappings": [
      {
        "containerPort": 8125,
        "hostPort": 8125
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/ai-chatbot-task-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  },
  {
    "image": "${fluent_bit_repository_url}:2.3",
    "name": "log_router",
    "essential": true,
    "cpu": 0,
    "portMappings": [],
    "user": "0",
    "volumesFrom": [],
    "firelensConfiguration": {
      "type": "fluentbit",
      "options": {
        "config-file-type": "file",
        "config-file-value": "/general/general.conf"
      }
    },
    "environment": [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "AIChatbot"
      },
      {
        "name": "DD_ENV",
        "value": "staging"
      },
      {
        "name": "DD_SERVICE",
        "value": "ai-chatbot-service"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/ai-chatbot-task"
      },
      {
        "name": "DD_API_KEY",
        "value": "********************************"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/ai-chatbot-task-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50
  }
]
