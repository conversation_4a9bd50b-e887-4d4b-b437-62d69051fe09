[
  {
    "name": "${container_name}",
    "image": "${app_image}",
    "cpu": 502,
    "memory": 1792,
    "essential": true,
    "environment" : [
      {
        "name": "MICRONAUT_ENVIRONMENTS",
        "value": "${environment}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "DD_AGENT_HOST",
        "value": "datadog-daemon"
      },
      {
        "name": "DD_TRACE_AGENT_PORT",
        "value": "8126"
      }
    ],
    "dockerLabels": {
      "com.datadoghq.tags.env": "${environment}",
      "com.datadoghq.tags.service": "${service_name}"
    },
    "secrets": [
      {
        "name": "INTEGRATIONS_FACETEC_SDK_DEVICE_KEY_IDENTIFIER",
        "valueFrom": "${secret_arns[1]}:DEVICE_KEY_IDENTIFIER::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_SDK_SERVER_KEY",
        "valueFrom": "${secret_arns[1]}:SERVER_KEY::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_SDK_PRODUCTION_KEY_TEXT",
        "valueFrom": "${secret_arns[1]}:PRODUCTION_KEY_TEXT::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_CLIENT_DEVICE_KEY_IDENTIFIER",
        "valueFrom": "${secret_arns[1]}:DEVICE_KEY_IDENTIFIER::"
      },
      {
        "name": "INTEGRATIONS_FACETEC_CLIENT_PUBLIC_FACE_SCAN_ENCRYPTION_KEY",
        "valueFrom": "${secret_arns[1]}:PUBLIC_FACE_SCAN_ENCRYPTION_KEY::"
      }
    ],
    "portMappings": [
      {
        "containerPort": ${app_port},
        "hostPort": ${app_port}
      }
    ],
    "volumesFrom": [],
    "logConfiguration": {
      "logDriver": "awsfirelens",
      "options": {
        "Name": "cloudwatch",
        "region": "us-east-1",
        "log_group_name": "/ecs/${service_name}-task",
        "auto_create_group": "false",
        "log_stream_name": "ecs/${service_name}-task/$(ecs_task_id)"
      }
    }
  },
  {
    "image": "${fluent_bit_repository_url}:2.3",
    "name": "log_router",
    "essential": true,
    "cpu": 0,
    "portMappings": [],
    "user": "0",
    "volumesFrom": [],
    "firelensConfiguration": {
      "type": "fluentbit",
      "options": {
        "config-file-type": "file",
        "config-file-value": "/general/general.conf"
      }
    },
    "secrets": [
      {
        "name": "DD_API_KEY",
        "valueFrom": "${secret_arns[0]}:DD_API_KEY::"
      }
    ],
    "environment": [
      {
        "name": "SERVICE_CONTAINER_NAME",
        "value": "${container_name}"
      },
      {
        "name": "DD_ENV",
        "value": "${environment}"
      },
      {
        "name": "DD_SERVICE",
        "value": "${service_name}"
      },
      {
        "name": "LOG_GROUP_NAME",
        "value": "/logs/${service_name}-task"
      },
      {
        "name": "REGION",
        "value": "us-east-1"
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${service_name}-dd",
        "awslogs-region": "us-east-1",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "memoryReservation": 50
  }
]
