[{"name": "Via2Service", "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/via2-service:${app_version}", "cpu": 502, "memory": 974, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "staging"}, {"name": "JAVA_OPTS", "value": "-Xmx974m"}], "secrets": [{"name": "WHATSAPP_TOKEN", "valueFrom": "arn:aws:secretsmanager:us-east-1:402556871325:secret:friday/whatsapp_key-jkXNcc:TOKEN::"}], "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/via2-task", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]