[{"name": "DDABills", "image": "402556871325.dkr.ecr.us-east-1.amazonaws.com/dda-bills:${app_version}", "cpu": 246, "memory": 768, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "staging"}, {"name": "JAVA_OPTS", "value": "-Xmx256m"}], "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dda-bills-task", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]