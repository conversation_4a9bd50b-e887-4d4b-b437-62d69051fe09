[{"name": "ConnectUtility", "image": "402556871325.dkr.ecr.sa-east-1.amazonaws.com/connect-utility:${app_version}", "cpu": 512, "memory": 2048, "essential": true, "environment": [{"name": "MICRONAUT_ENVIRONMENTS", "value": "staging"}, {"name": "JAVA_OPTS", "value": "-Xmx1536m"}], "dockerLabels": {"com.datadoghq.tags.env": "staging", "com.datadoghq.tags.service": "connect-utility-service"}, "portMappings": [{"containerPort": 8443, "hostPort": 8443}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/connect-utility-task-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}, {"name": "ConnectUtilityOcr", "image": "402556871325.dkr.ecr.sa-east-1.amazonaws.com/connect-utility-ocr:${app_version_2}", "cpu": 0, "essential": true, "environment": [{"name": "PORT", "value": "8037"}], "portMappings": [{"containerPort": 8037}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/connect-utility-task-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}, {"name": "ConnectUtilitySpeech2Text", "image": "402556871325.dkr.ecr.sa-east-1.amazonaws.com/connect-utility-s2t:${app_version_3}", "cpu": 0, "essential": true, "environment": [{"name": "PORT", "value": "8029"}], "portMappings": [{"containerPort": 8029}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/connect-utility-task-dd", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}]