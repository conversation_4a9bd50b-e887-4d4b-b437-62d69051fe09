locals {
  account_id                          = "************"
  production_availability_zones       = ["sa-east-1a", "sa-east-1b"]
  ecs_sqs_list_policy_resource        = ["arn:aws:sqs:us-east-1:${local.account_id}:*"]
  errors_bucket_name                  = "connect-utility-errors-stg"
  bills_bucket_name                   = "connect-utility-bills-stg"
  connect_utility_ocr_repository_name = "connect-utility-ocr"
  connect_utility_s2t_repository_name = "connect-utility-s2t"
}

data "terraform_remote_state" "us-east" {
  backend = "s3"
  config  = {
    bucket = "stg2-backend-infrastructure"
    key    = "stg/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [local.account_id]
}

module "backend" {
  source              = "../../modules/backend"
  environment         = var.environment
  backend_bucket_name = "sa-************-backend-infrastructure"
  dynamodb_table_name = "backend-infrastructure-lock-table"
}

terraform {
  backend "s3" {
    bucket         = "sa-************-backend-infrastructure"
    key            = "stg/terraform.tfstate"
    dynamodb_table = "backend-infrastructure-lock-table"
    encrypt        = true
    region         = "sa-east-1"
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "2.15.0"
  name    = "friday-vpc"
  cidr    = "10.0.0.0/16"

  azs             = ["sa-east-1a", "sa-east-1b"]
  public_subnets  = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnets = ["10.0.101.0/24", "10.0.102.0/24"]

  enable_nat_gateway = var.nat_gateway_enabled
  single_nat_gateway = true
  enable_vpn_gateway = false

  tags = {
    Terraform   = "true"
    Environment = var.environment
  }
}

module "connect_utility_ocr_image_version" {
  source         = "../../modules/image_version"
  container_name = "ConnectUtilityOcr"
  service_name   = "connect-utility-service"
  cluster_name   = "connect-utility-cluster"
}

module "connect_utility_s2t_image_version" {
  source         = "../../modules/image_version"
  container_name = "ConnectUtilitySpeech2Text"
  service_name   = "connect-utility-service"
  cluster_name   = "connect-utility-cluster"
}

module "python_connect_utility_image_version" {
  source         = "../../modules/image_version"
  container_name = "PythonConnectUtility"
  service_name   = "connect-utility-service"
  cluster_name   = "connect-utility-cluster"
}

module "connect_utility_image_version" {
  source         = "../../modules/image_version"
  container_name = "ConnectUtility"
  service_name   = "connect-utility-service"
  cluster_name   = "connect-utility-cluster"
}

resource "aws_ecs_cluster" "friday_fargate_cluster" {
  name = "connect-utility-cluster"
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecr_repository" "fluent_bit_ecr" {
  image_tag_mutability = "IMMUTABLE"
  name                 = "custom-fluent-bit"
}

resource "aws_ecr_repository" "connect_utility_ocr_repository" {
  image_tag_mutability = "IMMUTABLE"
  name                 = local.connect_utility_ocr_repository_name
}

resource "aws_ecr_repository" "connect_utility_s2t_repository" {
  image_tag_mutability = "IMMUTABLE"
  name                 = local.connect_utility_s2t_repository_name
}

resource "aws_s3_bucket" "connect-utility-errors-bucket" {
  bucket = local.errors_bucket_name

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Environment = var.environment
  }
}

resource "aws_s3_bucket" "connect-utility-bills-bucket" {
  bucket = local.bills_bucket_name

  lifecycle {
    prevent_destroy = true
  }

  tags = {
    Environment = var.environment
  }
}


resource "aws_dynamodb_table" "connect_utility_table" {
  name         = "ConnectUtility"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "PartitionKey"
  range_key    = "ScanKey"

  attribute {
    name = "PartitionKey"
    type = "S"
  }

  attribute {
    name = "ScanKey"
    type = "S"
  }

  tags = {
    Environment = var.environment
    Project     = "ConnectUtility"
  }
}

module "friday_connect_utility_task" {
  source = "../../modules/fargate_task"

  prefix                     = "connect-utility"
  ecr_repository_name        = "connect-utility"
  fargate_cpu                = 1024
  fargate_memory             = 4096
  ephemeral_storage          = 100
  task_definition            = "${path.module}/task-definitions/connect-utility.json"
  dynamo_access_enabled      = true
  ecs_dynamo_policy_resource = [
    aws_dynamodb_table.connect_utility_table.arn,
    "${aws_dynamodb_table.connect_utility_table.arn}/*",
  ]
  s3_read_objects = true
  s3_bucket_arns  = [
    aws_s3_bucket.connect-utility-errors-bucket.arn,
    aws_s3_bucket.connect-utility-bills-bucket.arn,
    data.terraform_remote_state.us-east.outputs.incoming_emails_s3_arn
  ]
  sqs_access_enabled        = true
  textract_enabled          = true
  ecs_sqs_policy_resource   = local.ecs_sqs_list_policy_resource
  sns_access_enabled        = true
  ecs_sns_policy_resource   = [data.terraform_remote_state.us-east.outputs.incoming_emails_sns_arn]
  user_pool_arn_enabled     = false
  app_version               = module.connect_utility_image_version.image_tag
  app_version_2             = module.connect_utility_ocr_image_version.image_tag
  app_version_3             = module.connect_utility_s2t_image_version.image_tag
  kms_enabled               = true
  kms_key_arns              = [var.connect_utility_replica_key_arn]
  fluent_bit_repository_url = aws_ecr_repository.fluent_bit_ecr.repository_url
  aws_region                = var.aws_region
}

module "utility_account_dlq" {
  source  = "terraform-aws-modules/sqs/aws"
  version = "~> 2.0"
  name    = "utility_account_dlq"
}

module "connect_utility_service" {
  source = "../../modules/fargate_service"

  aws_ecs_cluster         = aws_ecs_cluster.friday_fargate_cluster
  aws_private_subnet_id   = module.vpc.private_subnets
  aws_public_subnet_id    = module.vpc.public_subnets
  aws_vpc_id              = module.vpc.vpc_id
  prefix                  = "connect-utility"
  container_name          = "ConnectUtility"
  app_port                = 8443
  app_count               = 1
  health_check_path       = "/health"
  task_definition         = module.friday_connect_utility_task
  certificate_arn_enabled = false
}