<!-- First line should be an H1: Badges on top please! -->
<!-- markdownlint-disable MD041/first-line-heading/first-line-h1 -->
[![Terraform registry](https://img.shields.io/github/v/release/cattle-ops/*********************runner?label=Terraform%20Registry)](https://registry.terraform.io/modules/cattle-ops/gitlab-runner/aws/)
[![Gitter](https://badges.gitter.im/*********************runner/Lobby.svg)](https://gitter.im/*********************runner/Lobby?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge)
[![Actions](https://github.com/cattle-ops/*********************runner/workflows/CI/badge.svg)](https://github.com/cattle-ops/*********************runner/actions)
[![Renovate](https://img.shields.io/badge/renovate-enabled-brightgreen?logo=renovate)](https://www.mend.io/renovate/)
<!-- markdownlint-enable MD041/first-line-heading/first-line-h1 -->

# Terraform module for GitLab auto-scaling runners on AWS spot instances <!-- omit in toc -->

💥 See [issue 819](https://github.com/cattle-ops/*********************runner/issues/819) on how to migrate to v7 smoothly.
💥 See [pr 1204](https://github.com/cattle-ops/*********************runner/pull/1204) on how to migrate to v8 smoothly.

This [Terraform](https://www.terraform.io/) modules creates a [GitLab Runner](https://docs.gitlab.com/runner/). A blog post
describes the original version of the runner. See the post at [040code](https://040code.github.io/2017/12/09/runners-on-the-spot/).
The original setup of the module is based on the blog post: [Auto scale GitLab CI runners and save 90% on EC2 costs](https://about.gitlab.com/2017/11/23/autoscale-ci-runners/).

The runners created by the module use spot instances by default for running the builds using the `docker+machine` executor.

- Shared cache in S3 with life cycle management to clear objects after x days.
- Logs streamed to CloudWatch.
- Runner agents registered automatically.

The runner supports 3 main scenarios:

1. GitLab CI docker-machine runner - one runner agent

   In this scenario the runner agent is running on a single EC2 node and runners are created by [docker machine](https://docs.gitlab.com/runner/configuration/autoscale.html)
   using spot instances. Runners will scale automatically based on the configuration. The module creates a S3 cache by default,
   which is shared across runners (spot instances).

   ![runners-default](https://github.com/cattle-ops/*********************runner/raw/main/assets/images/runner-default.png)

2. GitLab CI docker-machine runner - multiple runner agents

   In this scenario the multiple runner agents can be created with different configuration by instantiating the module multiple times.
   Runners will scale automatically based on the configuration. The S3 cache can be shared across runners by managing the cache
   outside the module.

   ![runners-cache](https://github.com/cattle-ops/*********************runner/raw/main/assets/images/runner-cache.png)

3. GitLab Ci docker runner

   In this scenario _not_ docker machine is used but docker to schedule the builds. Builds will run on the same EC2 instance as the
   agent. No auto-scaling is supported.

   ![runners-docker](https://github.com/cattle-ops/*********************runner/raw/main/assets/images/runner-docker.png)

For detailed concepts and usage please refer to [usage](docs/usage.md).

## Contributors ✨

PRs are welcome! Please see the [contributing guide](CONTRIBUTING.md) for more details.

Thanks to all the people who already contributed!

<!-- this is the only option to integrate the contributors list in the README.md -->
<!-- markdownlint-disable MD033 -->
<a href="https://github.com/cattle-ops/*********************runner/graphs/contributors">
  <!-- markdownlint-disable MD033 -->
  <img src="https://contrib.rocks/image?repo=cattle-ops/*********************runner" alt="contributors"/>
</a>

Made with [contributors-img](https://contrib.rocks).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Module Documentation

<!-- markdownlint-disable -->
<!-- cSpell:disable -->
<!-- markdown-link-check-disable -->
<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.3 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.76 |
| <a name="requirement_local"></a> [local](#requirement\_local) | >= 2.4.0 |
| <a name="requirement_tls"></a> [tls](#requirement\_tls) | >= 3 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.92.0 |
| <a name="provider_local"></a> [local](#provider\_local) | 2.5.2 |
| <a name="provider_tls"></a> [tls](#provider\_tls) | 4.0.6 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_cache"></a> [cache](#module\_cache) | ./modules/cache | n/a |
| <a name="module_terminate_agent_hook"></a> [terminate\_agent\_hook](#module\_terminate\_agent\_hook) | ./modules/terminate-agent-hook | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_autoscaling_group.autoscaler](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/autoscaling_group) | resource |
| [aws_autoscaling_group.gitlab_runner_instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/autoscaling_group) | resource |
| [aws_autoscaling_lifecycle_hook.wait_for_gitlab_runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/autoscaling_lifecycle_hook) | resource |
| [aws_autoscaling_schedule.scale_in](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/autoscaling_schedule) | resource |
| [aws_autoscaling_schedule.scale_out](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/autoscaling_schedule) | resource |
| [aws_cloudwatch_log_group.environment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_log_group) | resource |
| [aws_eip.gitlab_runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip) | resource |
| [aws_iam_instance_profile.docker_autoscaler](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.docker_machine](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_instance_profile.instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_policy.eip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.instance_docker_autoscaler_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.instance_docker_machine_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.instance_kms_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.instance_session_manager_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.service_linked_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.ssm](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role.docker_autoscaler](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.docker_machine](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy.instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy) | resource |
| [aws_iam_role_policy_attachment.docker_autoscaler_session_manager_aws_managed](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.docker_autoscaler_user_defined_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.docker_machine_cache_instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.docker_machine_session_manager_aws_managed](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.docker_machine_user_defined_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.eip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_docker_autoscaler_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_docker_machine_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_kms_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_session_manager_aws_managed](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_session_manager_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.service_linked_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.ssm](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.user_defined_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_key_pair.autoscaler](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/key_pair) | resource |
| [aws_key_pair.fleet](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/key_pair) | resource |
| [aws_kms_alias.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_alias) | resource |
| [aws_kms_key.default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/kms_key) | resource |
| [aws_launch_template.fleet_gitlab_runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/launch_template) | resource |
| [aws_launch_template.gitlab_runner_instance](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/launch_template) | resource |
| [aws_launch_template.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/launch_template) | resource |
| [aws_security_group.docker_autoscaler](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group.docker_machine](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group.runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_ssm_parameter.runner_registration_token](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_ssm_parameter.runner_sentry_dsn](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [aws_vpc_security_group_egress_rule.docker_autoscaler_egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_egress_rule.docker_machine](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_egress_rule.runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_egress_rule.runner_manager_to_docker_autoscaler_egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_egress_rule.runner_manager_to_docker_machine_egress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_egress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_autoscaler_ingress](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_autoscaler_internal_traffic](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine_docker_runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine_docker_self](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine_ping_runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine_ping_self](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine_ssh_runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.docker_machine_ssh_self](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.runner](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [aws_vpc_security_group_ingress_rule.runner_ping_group](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_security_group_ingress_rule) | resource |
| [local_file.config_toml](https://registry.terraform.io/providers/hashicorp/local/latest/docs/resources/file) | resource |
| [local_file.user_data](https://registry.terraform.io/providers/hashicorp/local/latest/docs/resources/file) | resource |
| [tls_private_key.autoscaler](https://registry.terraform.io/providers/hashicorp/tls/latest/docs/resources/private_key) | resource |
| [tls_private_key.fleet](https://registry.terraform.io/providers/hashicorp/tls/latest/docs/resources/private_key) | resource |
| [aws_ami.docker_autoscaler_by_filter](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami) | data source |
| [aws_ami.docker_machine_by_filter](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami) | data source |
| [aws_ami.runner_by_filter](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami) | data source |
| [aws_availability_zone.runners](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/availability_zone) | data source |
| [aws_caller_identity.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_iam_policy_document.ssm](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |
| [aws_subnet.runners](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnet) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_debug"></a> [debug](#input\_debug) | trace\_runner\_user\_data: Enable bash trace for the user data script on the Agent. Be aware this could log sensitive data such as you GitLab runner token.<br/>write\_runner\_config\_to\_file: When enabled, outputs the rendered config.toml file in the root module. Note that enabling this can<br/>                             potentially expose sensitive information.<br/>write\_runner\_user\_data\_to\_file: When enabled, outputs the rendered userdata.sh file in the root module. Note that enabling this<br/>                                can potentially expose sensitive information. | <pre>object({<br/>    trace_runner_user_data         = optional(bool, false)<br/>    write_runner_config_to_file    = optional(bool, false)<br/>    write_runner_user_data_to_file = optional(bool, false)<br/>  })</pre> | `{}` | no |
| <a name="input_enable_managed_kms_key"></a> [enable\_managed\_kms\_key](#input\_enable\_managed\_kms\_key) | Let the module manage a KMS key. Be-aware of the costs of an custom key. Do not specify a `kms_key_id` when `enable_kms` is set to `true`. | `bool` | `false` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | A name that identifies the environment, used as prefix and for tagging. | `string` | n/a | yes |
| <a name="input_iam_object_prefix"></a> [iam\_object\_prefix](#input\_iam\_object\_prefix) | Set the name prefix of all AWS IAM resources. | `string` | `""` | no |
| <a name="input_iam_permissions_boundary"></a> [iam\_permissions\_boundary](#input\_iam\_permissions\_boundary) | Name of permissions boundary policy to attach to AWS IAM roles | `string` | `""` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS key id to encrypt the resources. Ensure that CloudWatch and Runner/Runner Workers have access to the provided KMS key. | `string` | `""` | no |
| <a name="input_kms_managed_alias_name"></a> [kms\_managed\_alias\_name](#input\_kms\_managed\_alias\_name) | Alias added to the created KMS key. | `string` | `""` | no |
| <a name="input_kms_managed_deletion_rotation_window_in_days"></a> [kms\_managed\_deletion\_rotation\_window\_in\_days](#input\_kms\_managed\_deletion\_rotation\_window\_in\_days) | Key deletion/rotation window for the created KMS key. Set to 0 for no rotation/deletion window. | `number` | `7` | no |
| <a name="input_runner_ami_filter"></a> [runner\_ami\_filter](#input\_runner\_ami\_filter) | List of maps used to create the AMI filter for the Runner AMI. Must resolve to an Amazon Linux 1, 2 or 2023 image. | `map(list(string))` | <pre>{<br/>  "name": [<br/>    "al2023-ami-2023*-x86_64"<br/>  ]<br/>}</pre> | no |
| <a name="input_runner_ami_id"></a> [runner\_ami\_id](#input\_runner\_ami\_id) | The AMI ID of the Runner instance. | `string` | `""` | no |
| <a name="input_runner_ami_owners"></a> [runner\_ami\_owners](#input\_runner\_ami\_owners) | The list of owners used to select the AMI of the Runner instance. | `list(string)` | <pre>[<br/>  "amazon"<br/>]</pre> | no |
| <a name="input_runner_cloudwatch"></a> [runner\_cloudwatch](#input\_runner\_cloudwatch) | enable = Boolean used to enable or disable the CloudWatch logging.<br/>log\_group\_name = Option to override the default name (`environment`) of the log group. Requires `enable = true`.<br/>retention\_days = Retention for cloudwatch logs. Defaults to unlimited. Requires `enable = true`. | <pre>object({<br/>    enable         = optional(bool, true)<br/>    log_group_name = optional(string, null)<br/>    retention_days = optional(number, 0)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_egress_rules"></a> [runner\_egress\_rules](#input\_runner\_egress\_rules) | Map of Egress rules for the Runner Manager security group. | <pre>map(object({<br/>    from_port       = optional(number, null)<br/>    to_port         = optional(number, null)<br/>    protocol        = string<br/>    description     = string<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    prefix_list_id  = optional(string, null)<br/>    security_group  = optional(string, null)<br/>  }))</pre> | <pre>{<br/>  "allow_https_ipv4": {<br/>    "cidr_block": "0.0.0.0/0",<br/>    "description": "Allow HTTPS egress traffic",<br/>    "from_port": 443,<br/>    "protocol": "tcp",<br/>    "to_port": 443<br/>  },<br/>  "allow_https_ipv6": {<br/>    "description": "Allow HTTPS egress traffic (IPv6)",<br/>    "from_port": 443,<br/>    "ipv6_cidr_block": "::/0",<br/>    "protocol": "tcp",<br/>    "to_port": 443<br/>  }<br/>}</pre> | no |
| <a name="input_runner_enable_asg_recreation"></a> [runner\_enable\_asg\_recreation](#input\_runner\_enable\_asg\_recreation) | Enable automatic redeployment of the Runner's ASG when the Launch Configs change. | `bool` | `true` | no |
| <a name="input_runner_gitlab"></a> [runner\_gitlab](#input\_runner\_gitlab) | ca\_certificate = Trusted CA certificate bundle (PEM format).<br/>certificate = Certificate of the GitLab instance to connect to (PEM format).<br/>registration\_token = (deprecated, This is replaced by the `registration_token` in `runner_gitlab_registration_config`.) Registration token to use to register the Runner.<br/>runner\_version = Version of the [GitLab Runner](https://gitlab.com/gitlab-org/gitlab-runner/-/releases). Make sure that it is available for your AMI. See https://packages.gitlab.com/app/runner/gitlab-runner/search?dist=amazon%2F2023&filter=rpms&page=1&q=<br/>url = URL of the GitLab instance to connect to.<br/>url\_clone = URL of the GitLab instance to clone from. Use only if the agent can’t connect to the GitLab URL.<br/>access\_token\_secure\_parameter\_store\_name = (deprecated) The name of the SSM parameter to read the GitLab access token from. It must have the `api` scope and be pre created.<br/>preregistered\_runner\_token\_ssm\_parameter\_name = The name of the SSM parameter to read the preregistered GitLab Runner token from. | <pre>object({<br/>    ca_certificate                                = optional(string, "")<br/>    certificate                                   = optional(string, "")<br/>    registration_token                            = optional(string, "__REPLACED_BY_USER_DATA__") # deprecated, removed in 8.0.0<br/>    runner_version                                = optional(string, "16.0.3")<br/>    url                                           = optional(string, "")<br/>    url_clone                                     = optional(string, "")<br/>    access_token_secure_parameter_store_name      = optional(string, "gitlab-runner-access-token") # deprecated, removed in 8.0.0<br/>    preregistered_runner_token_ssm_parameter_name = optional(string, "")<br/>  })</pre> | n/a | yes |
| <a name="input_runner_gitlab_registration_config"></a> [runner\_gitlab\_registration\_config](#input\_runner\_gitlab\_registration\_config) | (deprecated, replaced by runner\_gitlab.preregistered\_runner\_token\_ssm\_parameter\_name) Configuration used to register the Runner. See the README for an example, or reference the examples in the examples directory of this repo. There is also a good GitLab documentation available at: https://docs.gitlab.com/ee/ci/runners/configure_runners.html | <pre>object({<br/>    registration_token = optional(string, "__GITLAB_REGISTRATION_TOKEN_FROM_SSM__") # deprecated, removed in 8.0.0<br/>    tag_list           = optional(string, "")                                       # deprecated, removed in 8.0.0<br/>    description        = optional(string, "")                                       # deprecated, removed in 8.0.0<br/>    type               = optional(string, "")                                       # mandatory if gitlab_runner_version >= 16.0.0 # deprecated, removed in 8.0.0<br/>    group_id           = optional(string, "")                                       # mandatory if type is group # deprecated, removed in 8.0.0<br/>    project_id         = optional(string, "")                                       # mandatory if type is project # deprecated, removed in 8.0.0<br/>    locked_to_project  = optional(string, "")                                       # deprecated, removed in 8.0.0<br/>    run_untagged       = optional(string, "")                                       # deprecated, removed in 8.0.0<br/>    maximum_timeout    = optional(string, "")                                       # deprecated, removed in 8.0.0<br/>    access_level       = optional(string, "not_protected")                          # this is the only mandatory field calling the GitLab get token for executor operation # deprecated, removed in 8.0.0<br/>  })</pre> | `{}` | no |
| <a name="input_runner_gitlab_registration_token_secure_parameter_store_name"></a> [runner\_gitlab\_registration\_token\_secure\_parameter\_store\_name](#input\_runner\_gitlab\_registration\_token\_secure\_parameter\_store\_name) | (deprecated, replaced by runner\_gitlab.preregistered\_runner\_token\_ssm\_parameter\_name) The name of the SSM parameter to read the GitLab Runner registration token from. | `string` | `"gitlab-runner-registration-token"` | no |
| <a name="input_runner_gitlab_token_secure_parameter_store"></a> [runner\_gitlab\_token\_secure\_parameter\_store](#input\_runner\_gitlab\_token\_secure\_parameter\_store) | Name of the Secure Parameter Store entry to hold the GitLab Runner token. | `string` | `"runner-token"` | no |
| <a name="input_runner_ingress_rules"></a> [runner\_ingress\_rules](#input\_runner\_ingress\_rules) | Map of Ingress rules for the Runner Manager security group. | <pre>map(object({<br/>    from_port       = optional(number, null)<br/>    to_port         = optional(number, null)<br/>    protocol        = string<br/>    description     = string<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    prefix_list_id  = optional(string, null)<br/>    security_group  = optional(string, null)<br/>  }))</pre> | `{}` | no |
| <a name="input_runner_install"></a> [runner\_install](#input\_runner\_install) | amazon\_ecr\_credential\_helper = Install amazon-ecr-credential-helper inside `userdata_pre_install` script<br/>docker\_machine\_download\_url = URL to download docker machine binary. If not set, the docker machine version will be used to download the binary.<br/>docker\_machine\_version = By default docker\_machine\_download\_url is used to set the docker machine version. This version will be ignored once `docker_machine_download_url` is set. The version number is maintained by the CKI project. Check out at https://gitlab.com/cki-project/docker-machine/-/releases<br/>pre\_install\_script = Script to run before installing the Runner<br/>post\_install\_script = Script to run after installing the Runner<br/>start\_script = Script to run after starting the Runner<br/>yum\_update = Update the yum packages before installing the Runner | <pre>object({<br/>    amazon_ecr_credential_helper = optional(bool, false)<br/>    docker_machine_download_url  = optional(string, "")<br/>    docker_machine_version       = optional(string, "0.16.2-gitlab.19-cki.5")<br/>    pre_install_script           = optional(string, "")<br/>    post_install_script          = optional(string, "")<br/>    start_script                 = optional(string, "")<br/>    yum_update                   = optional(bool, true)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_instance"></a> [runner\_instance](#input\_runner\_instance) | additional\_tags = Map of tags that will be added to the Runner instance.<br/>collect\_autoscaling\_metrics = A list of metrics to collect. The allowed values are GroupDesiredCapacity, GroupInServiceCapacity, GroupPendingCapacity, GroupMinSize, GroupMaxSize, GroupInServiceInstances, GroupPendingInstances, GroupStandbyInstances, GroupStandbyCapacity, GroupTerminatingCapacity, GroupTerminatingInstances, GroupTotalCapacity, GroupTotalInstances.<br/>ebs\_optimized = Enable EBS optimization for the Runner instance.<br/>max\_lifetime\_seconds = The maximum time a Runner should live before it is killed.<br/>monitoring = Enable the detailed monitoring on the Runner instance.<br/>name = Name of the Runner instance.<br/>name\_prefix = Set the name prefix and override the `Name` tag for the Runner instance.<br/>private\_address\_only = Restrict the Runner to use private IP addresses only. If this is set to `true` the Runner will use a private IP address only in case the Runner Workers use private addresses only.<br/>root\_device\_config = The Runner's root block device configuration. Takes the following keys: `device_name`, `delete_on_termination`, `volume_type`, `volume_size`, `encrypted`, `iops`, `throughput`, `kms_key_id`<br/>spot\_price = By setting a spot price bid price the Runner is created via a spot request. Be aware that spot instances can be stopped by AWS. Choose \"on-demand-price\" to pay up to the current on demand price for the instance type chosen.<br/>ssm\_access = Allows to connect to the Runner via SSM.<br/>type = EC2 instance type used.<br/>use\_eip = Assigns an EIP to the Runner. | <pre>object({<br/>    additional_tags             = optional(map(string))<br/>    collect_autoscaling_metrics = optional(list(string), null)<br/>    ebs_optimized               = optional(bool, true)<br/>    max_lifetime_seconds        = optional(number, null)<br/>    monitoring                  = optional(bool, true)<br/>    name                        = string<br/>    name_prefix                 = optional(string)<br/>    private_address_only        = optional(bool, true)<br/>    root_device_config          = optional(map(string), {})<br/>    spot_price                  = optional(string, null)<br/>    ssm_access                  = optional(bool, false)<br/>    type                        = optional(string, "t3.micro")<br/>    use_eip                     = optional(bool, false)<br/>  })</pre> | <pre>{<br/>  "name": "gitlab-runner"<br/>}</pre> | no |
| <a name="input_runner_manager"></a> [runner\_manager](#input\_runner\_manager) | For details check https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-global-section<br/><br/>gitlab\_check\_interval = Number of seconds between checking for available jobs (check\_interval)<br/>maximum\_concurrent\_jobs = The maximum number of jobs which can be processed by all Runners at the same time (concurrent).<br/>prometheus\_listen\_address = Defines an address (<host>:<port>) the Prometheus metrics HTTP server should listen on (listen\_address).<br/>sentry\_dsn = Sentry DSN of the project for the Runner Manager to use (uses legacy DSN format) (sentry\_dsn) | <pre>object({<br/>    gitlab_check_interval     = optional(number, 3)<br/>    maximum_concurrent_jobs   = optional(number, 10)<br/>    prometheus_listen_address = optional(string, "")<br/>    sentry_dsn                = optional(string, "__SENTRY_DSN_REPLACED_BY_USER_DATA__")<br/>  })</pre> | `{}` | no |
| <a name="input_runner_metadata_options"></a> [runner\_metadata\_options](#input\_runner\_metadata\_options) | Enable the Runner instance metadata service. IMDSv2 is enabled by default. | <pre>object({<br/>    http_endpoint               = string<br/>    http_tokens                 = string<br/>    http_put_response_hop_limit = number<br/>    instance_metadata_tags      = string<br/>  })</pre> | <pre>{<br/>  "http_endpoint": "enabled",<br/>  "http_put_response_hop_limit": 2,<br/>  "http_tokens": "required",<br/>  "instance_metadata_tags": "disabled"<br/>}</pre> | no |
| <a name="input_runner_networking"></a> [runner\_networking](#input\_runner\_networking) | allow\_incoming\_ping = Allow ICMP Ping to the Runner. Specify `allow_incoming_ping_security_group_ids` too!<br/>allow\_incoming\_ping\_security\_group\_ids = A list of security group ids that are allowed to ping the Runner.<br/>security\_group\_description = A description for the Runner's security group<br/>security\_group\_ids = IDs of security groups to add to the Runner. | <pre>object({<br/>    allow_incoming_ping                    = optional(bool, false)<br/>    allow_incoming_ping_security_group_ids = optional(list(string), [])<br/>    security_group_description             = optional(string, "A security group containing gitlab-runner agent instances")<br/>    security_group_ids                     = optional(list(string), [])<br/>  })</pre> | `{}` | no |
| <a name="input_runner_role"></a> [runner\_role](#input\_runner\_role) | additional\_tags = Map of tags that will be added to the role created. Useful for tag based authorization.<br/>allow\_iam\_service\_linked\_role\_creation = Boolean used to control attaching the policy to the Runner to create service linked roles.<br/>assume\_role\_policy\_json = The assume role policy for the Runner.<br/>create\_role\_profile = Whether to create the IAM role/profile for the Runner. If you provide your own role, make sure that it has the required permissions.<br/>policy\_arns = List of policy ARNs to be added to the instance profile of the Runner.<br/>role\_profile\_name = IAM role/profile name for the Runner. If unspecified then `${var.iam_object_prefix}-instance` is used. | <pre>object({<br/>    additional_tags                        = optional(map(string))<br/>    allow_iam_service_linked_role_creation = optional(bool, true)<br/>    assume_role_policy_json                = optional(string, "")<br/>    create_role_profile                    = optional(bool, true)<br/>    policy_arns                            = optional(list(string), [])<br/>    role_profile_name                      = optional(string)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_schedule_config"></a> [runner\_schedule\_config](#input\_runner\_schedule\_config) | Map containing the configuration of the ASG scale-out and scale-in for the Runner. Will only be used if `runner_schedule_enable` is set to `true`. | `map(any)` | <pre>{<br/>  "scale_in_count": 0,<br/>  "scale_in_recurrence": "0 18 * * 1-5",<br/>  "scale_in_time_zone": "Etc/UTC",<br/>  "scale_out_count": 1,<br/>  "scale_out_recurrence": "0 8 * * 1-5",<br/>  "scale_out_time_zone": "Etc/UTC"<br/>}</pre> | no |
| <a name="input_runner_schedule_enable"></a> [runner\_schedule\_enable](#input\_runner\_schedule\_enable) | Set to `true` to enable the auto scaling group schedule for the Runner. | `bool` | `false` | no |
| <a name="input_runner_sentry_secure_parameter_store_name"></a> [runner\_sentry\_secure\_parameter\_store\_name](#input\_runner\_sentry\_secure\_parameter\_store\_name) | The Sentry DSN name used to store the Sentry DSN in Secure Parameter Store | `string` | `"sentry-dsn"` | no |
| <a name="input_runner_terminate_ec2_environment_variables"></a> [runner\_terminate\_ec2\_environment\_variables](#input\_runner\_terminate\_ec2\_environment\_variables) | Environment variables to set for the Lambda function. A value of `{HANDLER} is replaced with the handler value of the Lambda function.` | `map(string)` | `{}` | no |
| <a name="input_runner_terminate_ec2_lambda_egress_rules"></a> [runner\_terminate\_ec2\_lambda\_egress\_rules](#input\_runner\_terminate\_ec2\_lambda\_egress\_rules) | Map of egress rules for the Lambda function. | <pre>map(object({<br/>    from_port       = optional(number, null)<br/>    to_port         = optional(number, null)<br/>    protocol        = string<br/>    description     = string<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    prefix_list_id  = optional(string, null)<br/>    security_group  = optional(string, null)<br/>  }))</pre> | <pre>{<br/>  "allow_https_ipv4": {<br/>    "cidr_block": "0.0.0.0/0",<br/>    "description": "Allow HTTPS egress traffic to all destinations (IPv4)",<br/>    "from_port": 443,<br/>    "protocol": "tcp",<br/>    "to_port": 443<br/>  },<br/>  "allow_https_ipv6": {<br/>    "description": "Allow HTTPS egress traffic to all destinations (IPv6)",<br/>    "from_port": 443,<br/>    "ipv6_cidr_block": "::/0",<br/>    "protocol": "tcp",<br/>    "to_port": 443<br/>  }<br/>}</pre> | no |
| <a name="input_runner_terminate_ec2_lambda_handler"></a> [runner\_terminate\_ec2\_lambda\_handler](#input\_runner\_terminate\_ec2\_lambda\_handler) | The handler for the terminate Lambda function. | `string` | `null` | no |
| <a name="input_runner_terminate_ec2_lambda_layer_arns"></a> [runner\_terminate\_ec2\_lambda\_layer\_arns](#input\_runner\_terminate\_ec2\_lambda\_layer\_arns) | A list of ARNs of Lambda layers to attach to the Lambda function. | `list(string)` | `[]` | no |
| <a name="input_runner_terminate_ec2_lifecycle_hook_name"></a> [runner\_terminate\_ec2\_lifecycle\_hook\_name](#input\_runner\_terminate\_ec2\_lifecycle\_hook\_name) | Specifies a custom name for the ASG terminate lifecycle hook and related resources. | `string` | `null` | no |
| <a name="input_runner_terminate_ec2_lifecycle_timeout_duration"></a> [runner\_terminate\_ec2\_lifecycle\_timeout\_duration](#input\_runner\_terminate\_ec2\_lifecycle\_timeout\_duration) | Amount of time in seconds to wait for GitLab Runner to finish picked up jobs. Defaults to the `maximum_timeout` configured + `5m`. Maximum allowed is `7200` (2 hours) | `number` | `null` | no |
| <a name="input_runner_terminate_ec2_timeout_duration"></a> [runner\_terminate\_ec2\_timeout\_duration](#input\_runner\_terminate\_ec2\_timeout\_duration) | Timeout in seconds for the graceful terminate worker Lambda function. | `number` | `90` | no |
| <a name="input_runner_terraform_timeout_delete_asg"></a> [runner\_terraform\_timeout\_delete\_asg](#input\_runner\_terraform\_timeout\_delete\_asg) | Timeout when trying to delete the Runner ASG. | `string` | `"10m"` | no |
| <a name="input_runner_worker"></a> [runner\_worker](#input\_runner\_worker) | For detailed information, check https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runners-section.<br/><br/>environment\_variables = List of environment variables to add to the Runner Worker (environment).<br/>max\_jobs = Number of jobs which can be processed in parallel by the Runner Worker.<br/>output\_limit = Sets the maximum build log size in kilobytes. Default is 4MB (output\_limit).<br/>request\_concurrency = Limit number of concurrent requests for new jobs from GitLab (default 1) (request\_concurrency).<br/>ssm\_access = Allows to connect to the Runner Worker via SSM.<br/>type = The Runner Worker type to use. Currently supports `docker+machine` or `docker` or `docker-autoscaler`.<br/>use\_private\_key = Use a private key to connect the Runner Manager to the Runner Workers. Ignored when fleeting is enabled (defaults to `true`). | <pre>object({<br/>    environment_variables = optional(list(string), [])<br/>    max_jobs              = optional(number, 0)<br/>    output_limit          = optional(number, 4096)<br/>    request_concurrency   = optional(number, 1)<br/>    ssm_access            = optional(bool, false)<br/>    type                  = optional(string, "docker+machine")<br/>    # false positive, use_private_key is not a secret<br/>    # kics-scan ignore-line<br/>    use_private_key = optional(bool, false)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_cache"></a> [runner\_worker\_cache](#input\_runner\_worker\_cache) | Configuration to control the creation of the cache bucket. By default the bucket will be created and used as shared<br/>cache. To use the same cache across multiple Runner Worker disable the creation of the cache and provide a policy and<br/>bucket name. See the public runner example for more details."<br/><br/>For detailed documentation check https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runnerscaches3-section.<br/><br/>access\_log\_bucker\_id = The ID of the bucket where the access logs are stored.<br/>access\_log\_bucket\_prefix = The bucket prefix for the access logs.<br/>authentication\_type = A string that declares the AuthenticationType for [runners.cache.s3]. Can either be 'iam' or 'credentials'.<br/>bucket = Name of the cache bucket. Requires `create = false`.<br/>bucket\_prefix = Prefix for s3 cache bucket name. Requires `create = true`.<br/>create = Boolean used to enable or disable the creation of the cache bucket.<br/>create\_aws\_s3\_bucket\_public\_access\_block = Boolean used to enable or disable the creation of the public access block for the cache bucket. Useful when organizations do not allow the creation of public access blocks on individual buckets (e.g. public access is blocked on all buckets at the organization level).<br/>expiration\_days = Number of days before cache objects expire. Requires `create = true`.<br/>include\_account\_id = Boolean used to include the account id in the cache bucket name. Requires `create = true`.<br/>policy = Policy to use for the cache bucket. Requires `create = false`.<br/>random\_suffix = Boolean used to enable or disable the use of a random string suffix on the cache bucket name. Requires `create = true`.<br/>shared = Boolean used to enable or disable the use of the cache bucket as shared cache.<br/>versioning = Boolean used to enable versioning on the cache bucket. Requires `create = true`. | <pre>object({<br/>    access_log_bucket_id                     = optional(string, null)<br/>    access_log_bucket_prefix                 = optional(string, null)<br/>    authentication_type                      = optional(string, "iam")<br/>    bucket                                   = optional(string, "")<br/>    bucket_prefix                            = optional(string, "")<br/>    create                                   = optional(bool, true)<br/>    create_aws_s3_bucket_public_access_block = optional(bool, true)<br/>    expiration_days                          = optional(number, 1)<br/>    include_account_id                       = optional(bool, true)<br/>    policy                                   = optional(string, "")<br/>    random_suffix                            = optional(bool, false)<br/>    shared                                   = optional(bool, false)<br/>    versioning                               = optional(bool, false)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_add_dind_volumes"></a> [runner\_worker\_docker\_add\_dind\_volumes](#input\_runner\_worker\_docker\_add\_dind\_volumes) | Add certificates and docker.sock to the volumes to support docker-in-docker (dind) | `bool` | `false` | no |
| <a name="input_runner_worker_docker_autoscaler"></a> [runner\_worker\_docker\_autoscaler](#input\_runner\_worker\_docker\_autoscaler) | fleeting\_plugin\_version = The version of aws fleeting plugin.<br/>connector\_config\_user = User to connect to worker machine.<br/>key\_pair\_name = The name of the key pair used by the Runner to connect to the docker-machine Runner Workers. This variable is only supported when `enables` is set to `true`.<br/>capacity\_per\_instance = The number of jobs that can be executed concurrently by a single instance.<br/>max\_use\_count = Max job number that can run on a worker.<br/>update\_interval = The interval to check with the fleeting plugin for instance updates.<br/>update\_interval\_when\_expecting = The interval to check with the fleeting plugin for instance updates when expecting a state change.<br/>instance\_ready\_command = Executes this command on each instance provisioned by the autoscaler to ensure that it is ready for use. A failure results in the instance being removed. | <pre>object({<br/>    fleeting_plugin_version        = optional(string, "1.0.0")<br/>    connector_config_user          = optional(string, "ec2-user")<br/>    key_pair_name                  = optional(string, "runner-worker-key")<br/>    capacity_per_instance          = optional(number, 1)<br/>    max_use_count                  = optional(number, 100)<br/>    update_interval                = optional(string, "1m")<br/>    update_interval_when_expecting = optional(string, "2s")<br/>    instance_ready_command         = optional(string, "")<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_autoscaler_ami_filter"></a> [runner\_worker\_docker\_autoscaler\_ami\_filter](#input\_runner\_worker\_docker\_autoscaler\_ami\_filter) | List of maps used to create the AMI filter for the Runner Worker (autoscaler). | `map(list(string))` | <pre>{<br/>  "name": [<br/>    "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"<br/>  ]<br/>}</pre> | no |
| <a name="input_runner_worker_docker_autoscaler_ami_id"></a> [runner\_worker\_docker\_autoscaler\_ami\_id](#input\_runner\_worker\_docker\_autoscaler\_ami\_id) | The ID of the AMI to use for the Runner Worker (autoscaler). | `string` | `""` | no |
| <a name="input_runner_worker_docker_autoscaler_ami_owners"></a> [runner\_worker\_docker\_autoscaler\_ami\_owners](#input\_runner\_worker\_docker\_autoscaler\_ami\_owners) | The list of owners used to select the AMI of the Runner Worker (autoscaler). | `list(string)` | <pre>[<br/>  "099720109477"<br/>]</pre> | no |
| <a name="input_runner_worker_docker_autoscaler_asg"></a> [runner\_worker\_docker\_autoscaler\_asg](#input\_runner\_worker\_docker\_autoscaler\_asg) | enabled\_metrics = List of metrics to collect.<br/>enable\_mixed\_instances\_policy = Make use of autoscaling-group mixed\_instances\_policy capacities to leverage pools and spot instances.<br/>health\_check\_grace\_period = Time (in seconds) after instance comes into service before checking health.<br/>health\_check\_type = Controls how health checking is done. Values are - EC2 and ELB.<br/>instance\_refresh\_min\_healthy\_percentage = The amount of capacity in the Auto Scaling group that must remain healthy during an instance refresh to allow the operation to continue, as a percentage of the desired capacity of the Auto Scaling group.<br/>instance\_refresh\_triggers = Set of additional property names that will trigger an Instance Refresh. A refresh will always be triggered by a change in any of launch\_configuration, launch\_template, or mixed\_instances\_policy.<br/>on\_demand\_base\_capacity = Absolute minimum amount of desired capacity that must be fulfilled by on-demand instances.<br/>on\_demand\_percentage\_above\_base\_capacity = Percentage split between on-demand and Spot instances above the base on-demand capacity.<br/>spot\_allocation\_strategy = How to allocate capacity across the Spot pools. 'lowest-price' to optimize cost, 'capacity-optimized' to reduce interruptions.<br/>spot\_instance\_pools = Number of Spot pools per availability zone to allocate capacity. EC2 Auto Scaling selects the cheapest Spot pools and evenly allocates Spot capacity across the number of Spot pools that you specify.<br/>subnet\_ids = The list of subnet IDs to use for the Runner Worker when the fleet mode is enabled.<br/>default\_instance\_type = Default instance type for the launch template<br/>types = The type of instance to use for the Runner Worker. In case of fleet mode, multiple instance types are supported.<br/>upgrade\_strategy = Auto deploy new instances when launch template changes. Can be either 'bluegreen', 'rolling' or 'off'.<br/>instance\_requirements = Override the instance type in the Launch Template with instance types that satisfy the requirements. | <pre>object({<br/>    enabled_metrics                          = optional(list(string), [])<br/>    enable_mixed_instances_policy            = optional(bool, false)<br/>    health_check_grace_period                = optional(number, 300)<br/>    health_check_type                        = optional(string, "EC2")<br/>    instance_refresh_min_healthy_percentage  = optional(number, 90)<br/>    instance_refresh_triggers                = optional(list(string), [])<br/>    on_demand_base_capacity                  = optional(number, 0)<br/>    on_demand_percentage_above_base_capacity = optional(number, 100)<br/>    spot_allocation_strategy                 = optional(string, "lowest-price")<br/>    spot_instance_pools                      = optional(number, 2)<br/>    subnet_ids                               = optional(list(string), [])<br/>    default_instance_type                    = optional(string, "m5.large")<br/>    types                                    = optional(list(string), [])<br/>    upgrade_strategy                         = optional(string, "rolling")<br/>    instance_requirements = optional(list(object({<br/>      allowed_instance_types = optional(list(string), [])<br/>      cpu_manufacturers      = optional(list(string), [])<br/>      instance_generations   = optional(list(string), [])<br/>      burstable_performance  = optional(string)<br/>      memory_mib = optional(object({<br/>        min = optional(number, null)<br/>      max = optional(number, null) }), {})<br/>      vcpu_count = optional(object({<br/>        min = optional(number, null)<br/>      max = optional(number, null) }), {})<br/>    })), [])<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_autoscaler_autoscaling_options"></a> [runner\_worker\_docker\_autoscaler\_autoscaling\_options](#input\_runner\_worker\_docker\_autoscaler\_autoscaling\_options) | Set autoscaling parameters based on periods, see https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runnersautoscalerpolicy-sections | <pre>list(object({<br/>    periods            = list(string)<br/>    timezone           = optional(string, "UTC")<br/>    idle_count         = optional(number)<br/>    idle_time          = optional(string)<br/>    scale_factor       = optional(number)<br/>    scale_factor_limit = optional(number, 0)<br/>  }))</pre> | `[]` | no |
| <a name="input_runner_worker_docker_autoscaler_instance"></a> [runner\_worker\_docker\_autoscaler\_instance](#input\_runner\_worker\_docker\_autoscaler\_instance) | ebs\_optimized = Enable EBS optimization for the Runner Worker.<br/>http\_tokens = Whether or not the metadata service requires session tokens.<br/>http\_put\_response\_hop\_limit = The desired HTTP PUT response hop limit for instance metadata requests. The larger the number, the further instance metadata requests can travel.<br/>monitoring = Enable detailed monitoring for the Runner Worker.<br/>private\_address\_only = Restrict Runner Worker to the use of a private IP address. If `runner_instance.use_private_address_only` is set to `true` (default),<br/>root\_device\_name = The name of the root volume for the Runner Worker.<br/>root\_size = The size of the root volume for the Runner Worker.<br/>start\_script = Cloud-init user data that will be passed to the Runner Worker. Should not be base64 encrypted.<br/>volume\_type = The type of volume to use for the Runner Worker. `gp2`, `gp3`, `io1` or `io2` are supported.<br/>volume\_iops = Guaranteed IOPS for the volume. Only supported when using `gp3`, `io1` or `io2` as `volume_type`.<br/>volume\_throughput = Throughput in MB/s for the volume. Only supported when using `gp3` as `volume_type`. | <pre>object({<br/>    ebs_optimized               = optional(bool, true)<br/>    http_tokens                 = optional(string, "required")<br/>    http_put_response_hop_limit = optional(number, 2)<br/>    monitoring                  = optional(bool, false)<br/>    private_address_only        = optional(bool, true)<br/>    root_device_name            = optional(string, "/dev/sda1")<br/>    root_size                   = optional(number, 8)<br/>    start_script                = optional(string, "")<br/>    volume_type                 = optional(string, "gp2")<br/>    volume_throughput           = optional(number, 125)<br/>    volume_iops                 = optional(number, 3000)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_autoscaler_role"></a> [runner\_worker\_docker\_autoscaler\_role](#input\_runner\_worker\_docker\_autoscaler\_role) | additional\_tags = Map of tags that will be added to the Runner Worker.<br/>assume\_role\_policy\_json = Assume role policy for the Runner Worker.<br/>policy\_arns = List of ARNs of IAM policies to attach to the Runner Workers.<br/>profile\_name    = Name of the IAM profile to attach to the Runner Workers. | <pre>object({<br/>    additional_tags         = optional(map(string), {})<br/>    assume_role_policy_json = optional(string, "")<br/>    policy_arns             = optional(list(string), [])<br/>    profile_name            = optional(string, "")<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_machine_ami_filter"></a> [runner\_worker\_docker\_machine\_ami\_filter](#input\_runner\_worker\_docker\_machine\_ami\_filter) | List of maps used to create the AMI filter for the Runner Worker (docker-machine). | `map(list(string))` | <pre>{<br/>  "name": [<br/>    "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*"<br/>  ]<br/>}</pre> | no |
| <a name="input_runner_worker_docker_machine_ami_id"></a> [runner\_worker\_docker\_machine\_ami\_id](#input\_runner\_worker\_docker\_machine\_ami\_id) | The ID of the AMI to use for the Runner Worker (docker-machine). | `string` | `""` | no |
| <a name="input_runner_worker_docker_machine_ami_owners"></a> [runner\_worker\_docker\_machine\_ami\_owners](#input\_runner\_worker\_docker\_machine\_ami\_owners) | The list of owners used to select the AMI of the Runner Worker (docker-machine). | `list(string)` | <pre>[<br/>  "099720109477"<br/>]</pre> | no |
| <a name="input_runner_worker_docker_machine_autoscaling_options"></a> [runner\_worker\_docker\_machine\_autoscaling\_options](#input\_runner\_worker\_docker\_machine\_autoscaling\_options) | Set autoscaling parameters based on periods, see https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runnersmachine-section | <pre>list(object({<br/>    periods           = list(string)<br/>    idle_count        = optional(number)<br/>    idle_scale_factor = optional(number)<br/>    idle_count_min    = optional(number)<br/>    idle_time         = optional(number)<br/>    timezone          = optional(string, "UTC")<br/>  }))</pre> | `[]` | no |
| <a name="input_runner_worker_docker_machine_ec2_metadata_options"></a> [runner\_worker\_docker\_machine\_ec2\_metadata\_options](#input\_runner\_worker\_docker\_machine\_ec2\_metadata\_options) | Enable the Runner Worker metadata service. Requires you use CKI maintained docker machines. | <pre>object({<br/>    http_tokens                 = string<br/>    http_put_response_hop_limit = number<br/>  })</pre> | <pre>{<br/>  "http_put_response_hop_limit": 2,<br/>  "http_tokens": "required"<br/>}</pre> | no |
| <a name="input_runner_worker_docker_machine_ec2_options"></a> [runner\_worker\_docker\_machine\_ec2\_options](#input\_runner\_worker\_docker\_machine\_ec2\_options) | List of additional options for the docker+machine config. Each element of this list must be a key=value pair. E.g. '["amazonec2-zone=a"]' | `list(string)` | `[]` | no |
| <a name="input_runner_worker_docker_machine_fleet"></a> [runner\_worker\_docker\_machine\_fleet](#input\_runner\_worker\_docker\_machine\_fleet) | enable = Activates the fleet mode on the Runner. https://gitlab.com/cki-project/docker-machine/-/blob/v0.16.2-gitlab.19-cki.2/docs/drivers/aws.md#fleet-mode<br/>key\_pair\_name = The name of the key pair used by the Runner to connect to the docker-machine Runner Workers. This variable is only supported when `enables` is set to `true`. | <pre>object({<br/>    enable        = bool<br/>    key_pair_name = optional(string, "fleet-key")<br/>  })</pre> | <pre>{<br/>  "enable": false<br/>}</pre> | no |
| <a name="input_runner_worker_docker_machine_instance"></a> [runner\_worker\_docker\_machine\_instance](#input\_runner\_worker\_docker\_machine\_instance) | For detailed documentation check https://docs.gitlab.com/runner/configuration/advanced-configuration.html#the-runnersmachine-section<br/><br/>docker\_registry\_mirror\_url = The URL of the Docker registry mirror to use for the Runner Worker.<br/>destroy\_after\_max\_builds = Destroy the instance after the maximum number of builds has been reached.<br/>ebs\_optimized = Enable EBS optimization for the Runner Worker.<br/>idle\_count = Number of idle Runner Worker instances (not working for the Docker Runner Worker) (IdleCount).<br/>idle\_time = Idle time of the Runner Worker before they are destroyed (not working for the Docker Runner Worker) (IdleTime).<br/>max\_growth\_rate = The maximum number of machines that can be added to the runner in parallel.<br/>monitoring = Enable detailed monitoring for the Runner Worker.<br/>name\_prefix = Set the name prefix and override the `Name` tag for the Runner Worker.<br/>private\_address\_only = Restrict Runner Worker to the use of a private IP address. If `runner_instance.use_private_address_only` is set to `true` (default), `runner_worker_docker_machine_instance.private_address_only` will also apply for the Runner.<br/>root\_device\_name = The name of the root volume for the Runner Worker.<br/>root\_size = The size of the root volume for the Runner Worker.<br/>start\_script = Cloud-init user data that will be passed to the Runner Worker. Should not be base64 encrypted.<br/>subnet\_ids = The list of subnet IDs to use for the Runner Worker when the fleet mode is enabled.<br/>types = The type of instance to use for the Runner Worker. In case of fleet mode, multiple instance types are supported.<br/>volume\_type = The type of volume to use for the Runner Worker. `gp2`, `gp3`, `io1` or `io2` are supported.<br/>volume\_throughput = Throughput in MB/s for the volume. Only supported when using `gp3` as `volume_type`.<br/>volume\_iops = Guaranteed IOPS for the volume. Only supported when using `gp3`, `io1` or `io2` as `volume_type`. Works for fleeting only. See `runner_worker_docker_machine_fleet`. | <pre>object({<br/>    destroy_after_max_builds   = optional(number, 0)<br/>    docker_registry_mirror_url = optional(string, "")<br/>    ebs_optimized              = optional(bool, true)<br/>    idle_count                 = optional(number, 0)<br/>    idle_time                  = optional(number, 600)<br/>    max_growth_rate            = optional(number, 0)<br/>    monitoring                 = optional(bool, false)<br/>    name_prefix                = optional(string, "")<br/>    private_address_only       = optional(bool, true)<br/>    root_device_name           = optional(string, "/dev/sda1")<br/>    root_size                  = optional(number, 8)<br/>    start_script               = optional(string, "")<br/>    subnet_ids                 = optional(list(string), [])<br/>    types                      = optional(list(string), ["m5.large"])<br/>    volume_type                = optional(string, "gp2")<br/>    volume_throughput          = optional(number, 125)<br/>    volume_iops                = optional(number, 3000)<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_machine_instance_spot"></a> [runner\_worker\_docker\_machine\_instance\_spot](#input\_runner\_worker\_docker\_machine\_instance\_spot) | enable = Enable spot instances for the Runner Worker.<br/>max\_price = The maximum price willing to pay. By default the price is limited by the current on demand price for the instance type chosen. | <pre>object({<br/>    enable    = optional(bool, true)<br/>    max_price = optional(string, "on-demand-price")<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_machine_role"></a> [runner\_worker\_docker\_machine\_role](#input\_runner\_worker\_docker\_machine\_role) | additional\_tags = Map of tags that will be added to the Runner Worker.<br/>assume\_role\_policy\_json = Assume role policy for the Runner Worker.<br/>policy\_arns = List of ARNs of IAM policies to attach to the Runner Workers.<br/>profile\_name    = Name of the IAM profile to attach to the Runner Workers. | <pre>object({<br/>    additional_tags         = optional(map(string), {})<br/>    assume_role_policy_json = optional(string, "")<br/>    policy_arns             = optional(list(string), [])<br/>    profile_name            = optional(string, "")<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_docker_machine_security_group_description"></a> [runner\_worker\_docker\_machine\_security\_group\_description](#input\_runner\_worker\_docker\_machine\_security\_group\_description) | A description for the Runner Worker security group | `string` | `"A security group containing Runner Worker instances"` | no |
| <a name="input_runner_worker_docker_options"></a> [runner\_worker\_docker\_options](#input\_runner\_worker\_docker\_options) | Options added to the [runners.docker] section of config.toml to configure the Docker container of the Runner Worker. For<br/>    details check https://docs.gitlab.com/runner/configuration/advanced-configuration.html<br/><br/>    Default values if the option is not given:<br/>      disable\_cache = "false"<br/>      image         = "docker:18.03.1-ce"<br/>      privileged    = "true"<br/>      pull\_policy   = "always"<br/>      shm\_size      = 0<br/>      tls\_verify    = "false"<br/>      volumes       = "/cache" | <pre>object({<br/>    allowed_images               = optional(list(string))<br/>    allowed_pull_policies        = optional(list(string))<br/>    allowed_services             = optional(list(string))<br/>    cache_dir                    = optional(string)<br/>    cap_add                      = optional(list(string))<br/>    cap_drop                     = optional(list(string))<br/>    container_labels             = optional(list(string))<br/>    cpuset_cpus                  = optional(string)<br/>    cpu_shares                   = optional(number)<br/>    cpus                         = optional(string)<br/>    devices                      = optional(list(string))<br/>    device_cgroup_rules          = optional(list(string))<br/>    disable_cache                = optional(bool, false)<br/>    disable_entrypoint_overwrite = optional(bool)<br/>    dns                          = optional(list(string))<br/>    dns_search                   = optional(list(string))<br/>    extra_hosts                  = optional(list(string))<br/>    gpus                         = optional(string)<br/>    helper_image                 = optional(string)<br/>    helper_image_flavor          = optional(string)<br/>    host                         = optional(string)<br/>    hostname                     = optional(string)<br/>    image                        = optional(string, "docker:18.03.1-ce")<br/>    isolation                    = optional(string)<br/>    links                        = optional(list(string))<br/>    mac_address                  = optional(string)<br/>    memory                       = optional(string)<br/>    memory_swap                  = optional(string)<br/>    memory_reservation           = optional(string)<br/>    network_mode                 = optional(string)<br/>    oom_kill_disable             = optional(bool)<br/>    oom_score_adjust             = optional(number)<br/>    privileged                   = optional(bool, true)<br/>    pull_policies                = optional(list(string), ["always"])<br/>    runtime                      = optional(string)<br/>    security_opt                 = optional(list(string))<br/>    shm_size                     = optional(number, 0)<br/>    sysctls                      = optional(list(string))<br/>    tls_cert_path                = optional(string)<br/>    tls_verify                   = optional(bool, false)<br/>    user                         = optional(string)<br/>    userns_mode                  = optional(string)<br/>    volumes                      = optional(list(string), ["/cache"])<br/>    volumes_from                 = optional(list(string))<br/>    volume_driver                = optional(string)<br/>    wait_for_services_timeout    = optional(number)<br/>  })</pre> | <pre>{<br/>  "disable_cache": "false",<br/>  "image": "docker:18.03.1-ce",<br/>  "privileged": "true",<br/>  "pull_policies": [<br/>    "always"<br/>  ],<br/>  "shm_size": 0,<br/>  "tls_verify": "false",<br/>  "volumes": [<br/>    "/cache"<br/>  ]<br/>}</pre> | no |
| <a name="input_runner_worker_docker_services"></a> [runner\_worker\_docker\_services](#input\_runner\_worker\_docker\_services) | Starts additional services with the Docker container. All fields must be set (examine the Dockerfile of the service image for the entrypoint - see ./examples/runner-default/main.tf) | <pre>list(object({<br/>    name       = string<br/>    alias      = string<br/>    entrypoint = list(string)<br/>    command    = list(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_runner_worker_docker_services_volumes_tmpfs"></a> [runner\_worker\_docker\_services\_volumes\_tmpfs](#input\_runner\_worker\_docker\_services\_volumes\_tmpfs) | Mount a tmpfs in gitlab service container. https://docs.gitlab.com/runner/executors/docker.html#mounting-a-directory-in-ram | <pre>list(object({<br/>    volume  = string<br/>    options = string<br/>  }))</pre> | `[]` | no |
| <a name="input_runner_worker_docker_volumes_tmpfs"></a> [runner\_worker\_docker\_volumes\_tmpfs](#input\_runner\_worker\_docker\_volumes\_tmpfs) | Mount a tmpfs in Executor container. https://docs.gitlab.com/runner/executors/docker.html#mounting-a-directory-in-ram | <pre>list(object({<br/>    volume  = string<br/>    options = string<br/>  }))</pre> | `[]` | no |
| <a name="input_runner_worker_egress_rules"></a> [runner\_worker\_egress\_rules](#input\_runner\_worker\_egress\_rules) | Map of egress rules for the Runner workers | <pre>map(object({<br/>    from_port       = optional(number, null)<br/>    to_port         = optional(number, null)<br/>    protocol        = string<br/>    description     = string<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    prefix_list_id  = optional(string, null)<br/>    security_group  = optional(string, null)<br/>  }))</pre> | <pre>{<br/>  "allow_https_ipv4": {<br/>    "cidr_block": "0.0.0.0/0",<br/>    "description": "Allow HTTPS egress traffic to all destinations (IPv4)",<br/>    "from_port": 443,<br/>    "protocol": "tcp",<br/>    "to_port": 443<br/>  },<br/>  "allow_https_ipv6": {<br/>    "description": "Allow HTTPS egress traffic to all destinations (IPv6)",<br/>    "from_port": 443,<br/>    "ipv6_cidr_block": "::/0",<br/>    "protocol": "tcp",<br/>    "to_port": 443<br/>  },<br/>  "allow_ssh_ipv4": {<br/>    "cidr_block": "0.0.0.0/0",<br/>    "description": "Allow SSH egress traffic to all destinations (IPv4)",<br/>    "from_port": 22,<br/>    "protocol": "tcp",<br/>    "to_port": 22<br/>  },<br/>  "allow_ssh_ipv6": {<br/>    "description": "Allow SSH egress traffic to all destinations (IPv6)",<br/>    "from_port": 22,<br/>    "ipv6_cidr_block": "::/0",<br/>    "protocol": "tcp",<br/>    "to_port": 22<br/>  }<br/>}</pre> | no |
| <a name="input_runner_worker_gitlab_pipeline"></a> [runner\_worker\_gitlab\_pipeline](#input\_runner\_worker\_gitlab\_pipeline) | post\_build\_script = Script to execute in the pipeline just after the build, but before executing after\_script.<br/>pre\_build\_script = Script to execute in the pipeline just before the build.<br/>pre\_clone\_script = Script to execute in the pipeline before cloning the Git repository. this can be used to adjust the Git client configuration first, for example. | <pre>object({<br/>    post_build_script = optional(string, "\"\"")<br/>    pre_build_script  = optional(string, "\"\"")<br/>    pre_clone_script  = optional(string, "\"\"")<br/>  })</pre> | `{}` | no |
| <a name="input_runner_worker_ingress_rules"></a> [runner\_worker\_ingress\_rules](#input\_runner\_worker\_ingress\_rules) | Map of ingress rules for the Runner workers | <pre>map(object({<br/>    from_port       = optional(number, null)<br/>    to_port         = optional(number, null)<br/>    protocol        = string<br/>    description     = string<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    prefix_list_id  = optional(string, null)<br/>    security_group  = optional(string, null)<br/>  }))</pre> | `{}` | no |
| <a name="input_security_group_prefix"></a> [security\_group\_prefix](#input\_security\_group\_prefix) | Set the name prefix and overwrite the `Name` tag for all security groups. | `string` | `""` | no |
| <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id) | Subnet id used for the Runner and Runner Workers. Must belong to the `vpc_id`. In case the fleet mode is used, multiple subnets for<br/>the Runner Workers can be provided with runner\_worker\_docker\_machine\_instance.subnet\_ids. | `string` | n/a | yes |
| <a name="input_suppressed_tags"></a> [suppressed\_tags](#input\_suppressed\_tags) | List of tag keys which are automatically removed and never added as default tag by the module. | `list(string)` | `[]` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Map of tags that will be added to created resources. By default resources will be tagged with name and environment. | `map(string)` | `{}` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | The VPC used for the runner and runner workers. | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_runner_agent_role_arn"></a> [runner\_agent\_role\_arn](#output\_runner\_agent\_role\_arn) | ARN of the role used for the ec2 instance for the GitLab runner agent. |
| <a name="output_runner_agent_role_name"></a> [runner\_agent\_role\_name](#output\_runner\_agent\_role\_name) | Name of the role used for the ec2 instance for the GitLab runner agent. |
| <a name="output_runner_agent_sg_id"></a> [runner\_agent\_sg\_id](#output\_runner\_agent\_sg\_id) | ID of the security group attached to the GitLab runner agent. |
| <a name="output_runner_as_group_name"></a> [runner\_as\_group\_name](#output\_runner\_as\_group\_name) | Name of the autoscaling group for the gitlab-runner instance |
| <a name="output_runner_cache_bucket_arn"></a> [runner\_cache\_bucket\_arn](#output\_runner\_cache\_bucket\_arn) | ARN of the S3 for the build cache. |
| <a name="output_runner_cache_bucket_name"></a> [runner\_cache\_bucket\_name](#output\_runner\_cache\_bucket\_name) | Name of the S3 for the build cache. |
| <a name="output_runner_eip"></a> [runner\_eip](#output\_runner\_eip) | EIP of the Gitlab Runner |
| <a name="output_runner_launch_template_name"></a> [runner\_launch\_template\_name](#output\_runner\_launch\_template\_name) | The name of the runner's launch template. |
| <a name="output_runner_role_arn"></a> [runner\_role\_arn](#output\_runner\_role\_arn) | ARN of the role used for the docker machine runners. |
| <a name="output_runner_role_name"></a> [runner\_role\_name](#output\_runner\_role\_name) | Name of the role used for the docker machine runners. |
| <a name="output_runner_sg_id"></a> [runner\_sg\_id](#output\_runner\_sg\_id) | ID of the security group attached to the worker instances (docker machine/autoscaler runners). |
<!-- END_TF_DOCS -->
<!-- markdownlint-enable -->
<!-- cSpell:enable -->
<!-- markdown-link-check-enable -->
