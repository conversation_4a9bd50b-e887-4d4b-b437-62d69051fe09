{"Version": "2012-10-17", "Statement": [{"Action": ["ec2:DescribeKeyPairs", "ec2:TerminateInstances", "ec2:StopInstances", "ec2:StartInstances", "ec2:RunInstances", "ec2:RebootInstances", "ec2:C<PERSON>KeyPair", "ec2:DeleteKeyPair", "ec2:ImportKeyPair", "ec2:Describe*", "ec2:CreateTags", "ec2:RequestSpotInstances", "ec2:CancelSpotInstanceRequests", "ec2:DescribeSubnets", "ec2:AssociateIamInstanceProfile", "ec2:<PERSON><PERSON><PERSON><PERSON><PERSON>", "autoscaling:CompleteLifecycleAction", "autoscaling:DescribeLifecycleHooks"], "Effect": "Allow", "Resource": "*"}, {"Action": ["iam:PassRole"], "Effect": "Allow", "Resource": "${docker_machine_role_arn}"}]}