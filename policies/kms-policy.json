{"Version": "2012-10-17", "Statement": [{"Sid": "Enable IAM User Permissions", "Effect": "Allow", "Principal": {"AWS": ["arn:${partition}:iam::${account_id}:root"]}, "Action": "kms:*", "Resource": "*"}, {"Sid": "allowLoggingToCloudWatch", "Effect": "Allow", "Principal": {"Service": "logs.${aws_region}.amazonaws.com"}, "Action": ["kms:Encrypt*", "kms:Decrypt*", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:Describe*"], "Resource": ["*"]}, {"Sid": "Allow service-linked role use of the customer managed key", "Effect": "Allow", "Principal": {"AWS": ["arn:aws:iam::${account_id}:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"]}, "Action": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"], "Resource": "*"}, {"Sid": "Allow access through EBS for all principals in the account that are authorized to use EBS", "Effect": "Allow", "Principal": {"AWS": "*"}, "Action": ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:CreateGrant", "kms:DescribeKey"], "Resource": "*", "Condition": {"StringEquals": {"kms:CallerAccount": "${account_id}", "kms:ViaService": "ec2.${aws_region}.amazonaws.com"}}}]}