# Autoscaler config
  [runners.autoscaler]
    plugin = "fleeting-plugin-aws"

    capacity_per_instance = ${runners_capacity_per_instance}
    update_interval = "${runners_update_interval}"
    update_interval_when_expecting = "${runners_update_interval_when_expecting}"

    max_use_count = ${runners_max_use_count}
    max_instances = ${runners_max_instances}

    instance_ready_command="${runners_instance_ready_command}"

    [runners.autoscaler.plugin_config] # plugin specific configuration (see plugin documentation)
      name = "${docker_autoscaling_name}"     # AWS Autoscaling Group name

    [runners.autoscaler.connector_config]
      username          = "${connector_config_user}"
      use_external_addr = false
      %{~ if use_private_key ~}
      key_path = "/root/.ssh/id_rsa"
      %{~ endif ~}

%{~ for config in runners_autoscaling ~}
    [[runners.autoscaler.policy]]
    %{~ for key, value in config ~}
      ${key} = ${value}
      %{~ endfor ~}
%{~ endfor ~}

