concurrent = ${runners_concurrent}
check_interval = ${runners_check_interval}
sentry_dsn = "${sentry_dsn}"
log_format = "json"
listen_address = "${prometheus_listen_address}"

[[runners]]
  name = "${runners_name}"
  url = "${gitlab_url}"
  ${tls_ca_file}
  clone_url = "${gitlab_clone_url}"
  token = "${runners_token}"
  executor = "${runners_executor}"
  environment = ${runners_environment_vars}
  pre_build_script = ${runners_pre_build_script}
  post_build_script = ${runners_post_build_script}
  # GitLab Runner < 17, otherwise use pre_get_sources_script
  pre_clone_script = ${runners_pre_clone_script}
  pre_get_sources_script = ${runners_pre_clone_script}
  request_concurrency = ${runners_request_concurrency}
  output_limit = ${runners_output_limit}
  limit = ${runners_limit}

  ${runners_docker_options}

  ${runners_docker_services}

  [runners.docker.tmpfs]
    ${runners_volumes_tmpfs}

  [runners.docker.services_tmpfs]
    ${runners_services_volumes_tmpfs}

  [runners.cache]
    Type = "s3"
    Shared = ${shared_cache}
    [runners.cache.s3]
      AuthenticationType = "${auth_type}"
      ServerAddress = "s3.amazonaws.com"
      BucketName = "${bucket_name}"
      BucketLocation = "${aws_region}"
      Insecure = false

  ${runners_docker_autoscaler}

  ${runners_docker_machine}

%{~ for config in runners_machine_autoscaling ~}
    [[runners.machine.autoscaling]]
    %{~ for key, value in config ~}
      ${key} = ${value}
      %{~ endfor ~}
%{~ endfor ~}

