echo 'installing additional software for logging'
# installing in a loop to ensure the cli is installed.
for i in {1..7}
do
  echo "Attempt: ---- " $i
  yum install -y aws-cli rsyslog amazon-cloudwatch-agent jq && break || sleep 60
done

# Create the CloudWatch Logs configuration file
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json  <<- EOF 
{
    "agent": {
        "run_as_user": "root"
    },
    "metrics": {
        "metrics_collected": {
            "cpu": {
                "measurement": [
                    "cpu_usage_idle"
                ],
                "metrics_collection_interval": 300,
                "totalcpu": true
            },
            "mem": {
                "measurement": [
                    "mem_used_percent"
                ],
                "metrics_collection_interval": 300
            }
        }
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
                    {
                        "file_path": "/var/log/messages",
                        "log_group_name": "${log_group_name}",
                        "log_stream_name": "{instance_id}/messages"
                    },
                    {
                        "file_path": "/var/log/user-data.log",
                        "log_group_name": "${log_group_name}",
                        "log_stream_name": "{instance_id}/user-data"
                    }
                ]
            }
        }
    }
}

EOF

systemctl --now enable rsyslog
systemctl restart amazon-cloudwatch-agent
