# Examples

Please see the readme in per example for more details:

- [runner-certificates](runner-certificates/) Example showing how to add custom TLS certificates to the runner
- [runner-default](runner-default/) The default setup, private subnet, auto register, runner on spot instances.
- [runner-docker](runner-docker/) Runners run on the same instance as the agent.
- [runner-fleeting](runner-fleeting-plugin/) Runners using the AWS fleeting plugin
- [runner-public](runner-public/) Runner in a public subnet, auto register, runner on spot instances.

