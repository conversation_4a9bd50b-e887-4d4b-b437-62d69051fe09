# Examples

Please note - the examples provided serve two primary means:

1. Show users working examples of the various ways in which the module can be configured and features supported
2. A means of testing/validating module changes

Please do not mistake the examples provided as "best practices". It is up to users to consult the AWS service documentation for best practices, usage recommendations, etc.
